2025-06-27 20:00:40 [INFO] - processador_completo_tjsp_txt - Iniciando processamento: TXT: c:\Users\<USER>\Documents\augment-projects\MontSamm\tjsp_integrado\TJSP_Integrado\autosfiltrados.txt, Excel: c:\Users\<USER>\Documents\augment-projects\MontSamm\tjsp_integrado\TJSP_Integrado\resultados_completos_TJSP_TXT_20250627_200040.xlsx, Downloads: c:\Users\<USER>\Documents\augment-projects\MontSamm\tjsp_integrado\TJSP_Integrado\downloads_completos
2025-06-27 20:00:40 [INFO] - carregar_numeros_filtrados - Carregando números de: c:\Users\<USER>\Documents\augment-projects\MontSamm\tjsp_integrado\TJSP_Integrado\autosfiltrados.txt
2025-06-27 20:00:40 [INFO] - carregar_numeros_filtrados - Total números válidos carregados: 11116
2025-06-27 20:00:40 [INFO] - configurar_chrome_unificado - Usando diretório de dados do usuário: C:/Users/<USER>/ClineAutomationProfile_TJSP
2025-06-27 20:00:42 [ERROR] - iniciar_navegador_unificado - Erro ao iniciar o navegador: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff69ecf6f65+76917]
	GetHandleVerifier [0x0x7ff69ecf6fc0+77008]
	(No symbol) [0x0x7ff69eaa9dea]
	(No symbol) [0x0x7ff69eae8489]
	(No symbol) [0x0x7ff69eae2675]
	(No symbol) [0x0x7ff69eb35e9e]
	(No symbol) [0x0x7ff69eb35630]
	(No symbol) [0x0x7ff69eb28243]
	(No symbol) [0x0x7ff69eaf1431]
	(No symbol) [0x0x7ff69eaf21c3]
	GetHandleVerifier [0x0x7ff69efcd29d+3051437]
	GetHandleVerifier [0x0x7ff69efc78f3+3028483]
	GetHandleVerifier [0x0x7ff69efe588d+3151261]
	GetHandleVerifier [0x0x7ff69ed1182e+185662]
	GetHandleVerifier [0x0x7ff69ed196ef+218111]
	GetHandleVerifier [0x0x7ff69ecffae4+112628]
	GetHandleVerifier [0x0x7ff69ecffc99+113065]
	GetHandleVerifier [0x0x7ff69ece6c68+10616]
	BaseThreadInitThunk [0x0x7ffb788ce8d7+23]
	RtlUserThreadStart [0x0x7ffb7949c34c+44]
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\augment-projects\MontSamm\tjsp_integrado\TJSP_Integrado\TJSP_completo_TXT_filtrado.py", line 135, in iniciar_navegador_unificado
    driver = webdriver.Chrome(options=chrome_options)
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\chrome\webdriver.py", line 47, in __init__
    super().__init__(
    ~~~~~~~~~~~~~~~~^
        browser_name=DesiredCapabilities.CHROME["browserName"],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        keep_alive=keep_alive,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\chromium\webdriver.py", line 69, in __init__
    super().__init__(command_executor=executor, options=options)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 257, in __init__
    self.start_session(capabilities)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 356, in start_session
    response = self.execute(Command.NEW_SESSION, caps)["value"]
               ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 447, in execute
    self.error_handler.check_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff69ecf6f65+76917]
	GetHandleVerifier [0x0x7ff69ecf6fc0+77008]
	(No symbol) [0x0x7ff69eaa9dea]
	(No symbol) [0x0x7ff69eae8489]
	(No symbol) [0x0x7ff69eae2675]
	(No symbol) [0x0x7ff69eb35e9e]
	(No symbol) [0x0x7ff69eb35630]
	(No symbol) [0x0x7ff69eb28243]
	(No symbol) [0x0x7ff69eaf1431]
	(No symbol) [0x0x7ff69eaf21c3]
	GetHandleVerifier [0x0x7ff69efcd29d+3051437]
	GetHandleVerifier [0x0x7ff69efc78f3+3028483]
	GetHandleVerifier [0x0x7ff69efe588d+3151261]
	GetHandleVerifier [0x0x7ff69ed1182e+185662]
	GetHandleVerifier [0x0x7ff69ed196ef+218111]
	GetHandleVerifier [0x0x7ff69ecffae4+112628]
	GetHandleVerifier [0x0x7ff69ecffc99+113065]
	GetHandleVerifier [0x0x7ff69ece6c68+10616]
	BaseThreadInitThunk [0x0x7ffb788ce8d7+23]
	RtlUserThreadStart [0x0x7ffb7949c34c+44]

2025-06-27 20:00:43 [CRITICAL] - processador_completo_tjsp_txt - Erro crítico: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff69ecf6f65+76917]
	GetHandleVerifier [0x0x7ff69ecf6fc0+77008]
	(No symbol) [0x0x7ff69eaa9dea]
	(No symbol) [0x0x7ff69eae8489]
	(No symbol) [0x0x7ff69eae2675]
	(No symbol) [0x0x7ff69eb35e9e]
	(No symbol) [0x0x7ff69eb35630]
	(No symbol) [0x0x7ff69eb28243]
	(No symbol) [0x0x7ff69eaf1431]
	(No symbol) [0x0x7ff69eaf21c3]
	GetHandleVerifier [0x0x7ff69efcd29d+3051437]
	GetHandleVerifier [0x0x7ff69efc78f3+3028483]
	GetHandleVerifier [0x0x7ff69efe588d+3151261]
	GetHandleVerifier [0x0x7ff69ed1182e+185662]
	GetHandleVerifier [0x0x7ff69ed196ef+218111]
	GetHandleVerifier [0x0x7ff69ecffae4+112628]
	GetHandleVerifier [0x0x7ff69ecffc99+113065]
	GetHandleVerifier [0x0x7ff69ece6c68+10616]
	BaseThreadInitThunk [0x0x7ffb788ce8d7+23]
	RtlUserThreadStart [0x0x7ffb7949c34c+44]
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\augment-projects\MontSamm\tjsp_integrado\TJSP_Integrado\TJSP_completo_TXT_filtrado.py", line 489, in processador_completo_tjsp_txt
    driver = iniciar_navegador_unificado()
  File "c:\Users\<USER>\Documents\augment-projects\MontSamm\tjsp_integrado\TJSP_Integrado\TJSP_completo_TXT_filtrado.py", line 135, in iniciar_navegador_unificado
    driver = webdriver.Chrome(options=chrome_options)
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\chrome\webdriver.py", line 47, in __init__
    super().__init__(
    ~~~~~~~~~~~~~~~~^
        browser_name=DesiredCapabilities.CHROME["browserName"],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        keep_alive=keep_alive,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\chromium\webdriver.py", line 69, in __init__
    super().__init__(command_executor=executor, options=options)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 257, in __init__
    self.start_session(capabilities)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 356, in start_session
    response = self.execute(Command.NEW_SESSION, caps)["value"]
               ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 447, in execute
    self.error_handler.check_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff69ecf6f65+76917]
	GetHandleVerifier [0x0x7ff69ecf6fc0+77008]
	(No symbol) [0x0x7ff69eaa9dea]
	(No symbol) [0x0x7ff69eae8489]
	(No symbol) [0x0x7ff69eae2675]
	(No symbol) [0x0x7ff69eb35e9e]
	(No symbol) [0x0x7ff69eb35630]
	(No symbol) [0x0x7ff69eb28243]
	(No symbol) [0x0x7ff69eaf1431]
	(No symbol) [0x0x7ff69eaf21c3]
	GetHandleVerifier [0x0x7ff69efcd29d+3051437]
	GetHandleVerifier [0x0x7ff69efc78f3+3028483]
	GetHandleVerifier [0x0x7ff69efe588d+3151261]
	GetHandleVerifier [0x0x7ff69ed1182e+185662]
	GetHandleVerifier [0x0x7ff69ed196ef+218111]
	GetHandleVerifier [0x0x7ff69ecffae4+112628]
	GetHandleVerifier [0x0x7ff69ecffc99+113065]
	GetHandleVerifier [0x0x7ff69ece6c68+10616]
	BaseThreadInitThunk [0x0x7ffb788ce8d7+23]
	RtlUserThreadStart [0x0x7ffb7949c34c+44]

2025-06-27 20:00:43 [INFO] - processador_completo_tjsp_txt - Nenhum precatório válido processado para Excel.
2025-06-27 20:00:43 [INFO] - processador_completo_tjsp_txt - --- FIM DA EXECUÇÃO ---
