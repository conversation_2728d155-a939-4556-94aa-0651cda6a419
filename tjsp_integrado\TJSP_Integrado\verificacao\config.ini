# Configurações dos Verificadores TJSP
# Este arquivo pode ser usado para personalizar comportamentos dos scripts

[FILTRO_2001_2009]
# Configurações do Verificador de Filtro (2001-2009)
ano_minimo = 2001
ano_maximo = 2009
excluir_terminacao = 0500
pagina_inicial_padrao = 1

[DUPLICADOS_2010_PLUS]
# Configurações do Verificador de Duplicados (2010+)
ano_minimo = 2011
excluir_terminacao = 0500
pagina_inicial_padrao = 18800

[GERAL]
# Configurações gerais
nome_pdf = pdf.pdf
encoding_saida = utf-8
formato_timestamp = %Y%m%d_%H%M%S

[RELATORIOS]
# Configurações de relatórios
incluir_timestamp_arquivo = true
gerar_backup_csv = true
separador_csv = ;

[PERFORMANCE]
# Configurações de performance  
progresso_a_cada_n_paginas = 100
timeout_pagina_segundos = 30
max_memoria_mb = 2048

# NOTA: Este arquivo é informativo
# As configurações estão hardcoded nos scripts Python
# Para modificar, edite diretamente os arquivos .py
