2025-06-27 12:29:43 [INFO] - configurar_chrome_unificado - Usando diretório de dados do usuário: C:\Users\<USER>\ClineAutomationProfile_TJSP_sami_
2025-06-27 12:29:52 [ERROR] - iniciar_navegador_unificado - Erro ao iniciar o navegador: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff6c7a76f65+76917]
	GetHandleVerifier [0x0x7ff6c7a76fc0+77008]
	(No symbol) [0x0x7ff6c7829dea]
	(No symbol) [0x0x7ff6c7868489]
	(No symbol) [0x0x7ff6c7862675]
	(No symbol) [0x0x7ff6c78b5e9e]
	(No symbol) [0x0x7ff6c78b5630]
	(No symbol) [0x0x7ff6c78a8243]
	(No symbol) [0x0x7ff6c7871431]
	(No symbol) [0x0x7ff6c78721c3]
	GetHandleVerifier [0x0x7ff6c7d4d29d+3051437]
	GetHandleVerifier [0x0x7ff6c7d478f3+3028483]
	GetHandleVerifier [0x0x7ff6c7d6588d+3151261]
	GetHandleVerifier [0x0x7ff6c7a9182e+185662]
	GetHandleVerifier [0x0x7ff6c7a996ef+218111]
	GetHandleVerifier [0x0x7ff6c7a7fae4+112628]
	GetHandleVerifier [0x0x7ff6c7a7fc99+113065]
	GetHandleVerifier [0x0x7ff6c7a66c68+10616]
	BaseThreadInitThunk [0x0x7ffb788ce8d7+23]
	RtlUserThreadStart [0x0x7ffb7949c34c+44]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\MontSamm\tjsp_integrado\TJSP_Integrado\TJSP_completo.py", line 113, in iniciar_navegador_unificado
    driver = webdriver.Chrome(options=chrome_options)
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\chrome\webdriver.py", line 47, in __init__
    super().__init__(
    ~~~~~~~~~~~~~~~~^
        browser_name=DesiredCapabilities.CHROME["browserName"],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        keep_alive=keep_alive,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\chromium\webdriver.py", line 69, in __init__
    super().__init__(command_executor=executor, options=options)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 257, in __init__
    self.start_session(capabilities)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 356, in start_session
    response = self.execute(Command.NEW_SESSION, caps)["value"]
               ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 447, in execute
    self.error_handler.check_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff6c7a76f65+76917]
	GetHandleVerifier [0x0x7ff6c7a76fc0+77008]
	(No symbol) [0x0x7ff6c7829dea]
	(No symbol) [0x0x7ff6c7868489]
	(No symbol) [0x0x7ff6c7862675]
	(No symbol) [0x0x7ff6c78b5e9e]
	(No symbol) [0x0x7ff6c78b5630]
	(No symbol) [0x0x7ff6c78a8243]
	(No symbol) [0x0x7ff6c7871431]
	(No symbol) [0x0x7ff6c78721c3]
	GetHandleVerifier [0x0x7ff6c7d4d29d+3051437]
	GetHandleVerifier [0x0x7ff6c7d478f3+3028483]
	GetHandleVerifier [0x0x7ff6c7d6588d+3151261]
	GetHandleVerifier [0x0x7ff6c7a9182e+185662]
	GetHandleVerifier [0x0x7ff6c7a996ef+218111]
	GetHandleVerifier [0x0x7ff6c7a7fae4+112628]
	GetHandleVerifier [0x0x7ff6c7a7fc99+113065]
	GetHandleVerifier [0x0x7ff6c7a66c68+10616]
	BaseThreadInitThunk [0x0x7ffb788ce8d7+23]
	RtlUserThreadStart [0x0x7ffb7949c34c+44]

