#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Verificador de Duplicados TJSP - Filtro 2010+
Analisa PDF a partir da página 18800, aplica novo filtro (ano > 2010, não termina 0500)
Foca na detecção de duplicados e gera lista limpa para próximas etapas.
"""

import os
import sys
import re
import pandas as pd
import fitz  # PyMuPDF
from datetime import datetime
from collections import defaultdict, Counter

# --- Configurações ---
SCRIPT_DIR = r"C:\Users\<USER>\OneDrive\Documentos\VS Code\0 - Automação Completa\TJSP_Integrado\verificacao"
PDF_NOME = "pdf.pdf"
PDF_CAMINHO = os.path.join(SCRIPT_DIR, PDF_NOME)
PAGINA_INICIAL_PADRAO = 18644

def configurar_diretorios():
    """Cria o diretório se não existir"""
    if not os.path.exists(SCRIPT_DIR):
        os.makedirs(SCRIPT_DIR, exist_ok=True)
        print(f"📁 Diretório criado: {SCRIPT_DIR}")

def extrair_ano_processo(numero_autos):
    """Extrai o ano do número de autos"""
    match = re.search(r'\d{7}-\d{2}\.(\d{4})\.\d\.\d{2}\.\d{4}', numero_autos)
    return int(match.group(1)) if match else None

def aplicar_filtro_2010_plus(numero_autos):
    """
    Aplica o novo filtro: ano > 2010 e não termina com 0500
    Retorna: (aprovado, motivo_reprovacao, ano_extraido)
    """
    # Extrair ano
    ano = extrair_ano_processo(numero_autos)
    
    if ano is None:
        return False, "FORMATO_INVALIDO", None
    
    # Verificar critério de ano (> 2010)
    if ano <= 2010:
        return False, f"ANO_MENOR_IGUAL_2010_{ano}", ano
    
    # Verificar se termina com 0500
    if numero_autos.endswith('0500'):
        return False, "TERMINA_COM_0500", ano
    
    return True, "APROVADO", ano

def analisar_duplicados_detalhado(numeros_por_pagina):
    """
    Analisa duplicados de forma detalhada
    Retorna estrutura completa de análise de duplicação
    """
    print("🔍 Analisando duplicados de forma detalhada...")
    
    # Contar ocorrências globais
    contador_global = Counter()
    mapa_paginas_por_numero = defaultdict(list)
    
    for pagina, numeros in numeros_por_pagina.items():
        for numero in numeros:
            contador_global[numero] += 1
            mapa_paginas_por_numero[numero].append(pagina)
    
    # Classificar números
    numeros_unicos = []
    numeros_duplicados = []
    
    for numero, count in contador_global.items():
        if count == 1:
            numeros_unicos.append(numero)
        else:
            numeros_duplicados.append({
                'numero': numero,
                'total_ocorrencias': count,
                'paginas_encontradas': sorted(mapa_paginas_por_numero[numero]),
                'span_paginas': max(mapa_paginas_por_numero[numero]) - min(mapa_paginas_por_numero[numero]) + 1
            })
    
    # Ordenar duplicados por quantidade de ocorrências
    numeros_duplicados.sort(key=lambda x: x['total_ocorrencias'], reverse=True)
    
    return {
        'contador_global': contador_global,
        'mapa_paginas': dict(mapa_paginas_por_numero),
        'numeros_unicos': numeros_unicos,
        'numeros_duplicados': numeros_duplicados,
        'total_numeros_brutos': sum(contador_global.values()),
        'total_numeros_unicos': len(numeros_unicos),
        'total_duplicados_unicos': len(numeros_duplicados),
        'total_ocorrencias_duplicadas': sum(d['total_ocorrencias'] for d in numeros_duplicados)
    }

def processar_pdf_duplicados(caminho_pdf, pagina_inicial=PAGINA_INICIAL_PADRAO):
    """
    Processa PDF focando na detecção de duplicados com novo filtro
    """
    print(f"🚀 Iniciando verificação de duplicados - Filtro 2010+")
    print(f"📍 PDF: {os.path.basename(caminho_pdf)}")
    print(f"📄 Página inicial: {pagina_inicial}")
    
    if not os.path.exists(caminho_pdf):
        print(f"❌ Arquivo PDF não encontrado: {caminho_pdf}")
        return None
    
    # Abrir PDF
    try:
        doc_pdf = fitz.open(caminho_pdf)
        total_paginas = len(doc_pdf)
        print(f"📚 PDF aberto. Total de páginas: {total_paginas}")
    except Exception as e:
        print(f"❌ Erro ao abrir PDF: {e}")
        return None
    
    # Validar página inicial
    if not (1 <= pagina_inicial <= total_paginas):
        print(f"⚠️ Página inicial ({pagina_inicial}) inválida. Usando página 1.")
        pagina_inicial = 1
    
    print(f"🔍 Processando da página {pagina_inicial} até {total_paginas}")
    print("="*90)
    
    # Estruturas de coleta
    numeros_brutos_por_pagina = {}  # Todos os números encontrados
    numeros_aprovados_por_pagina = {}  # Apenas os aprovados no filtro
    detalhes_por_numero = []
    estatisticas_por_pagina = []
    contadores = defaultdict(int)
    anos_encontrados = defaultdict(int)
    motivos_reprovacao = defaultdict(int)
    
    # Processar páginas
    for num_pagina in range(pagina_inicial - 1, total_paginas):
        pagina_display = num_pagina + 1
        
        if pagina_display % 100 == 0 or pagina_display == pagina_inicial:
            print(f"\n📄 Processando página {pagina_display}/{total_paginas}")
        
        try:
            # Extrair texto da página
            pagina = doc_pdf.load_page(num_pagina)
            texto_pagina = pagina.get_text("text")
            
            # Encontrar números de autos
            numeros_encontrados = re.findall(r'\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4}', texto_pagina)
            numeros_unicos_pagina = list(set(numeros_encontrados))
            
            # Armazenar números brutos
            numeros_brutos_por_pagina[pagina_display] = numeros_encontrados
            
            # Aplicar filtro e coletar aprovados
            numeros_aprovados_pagina = []
            aprovados_pagina = 0
            reprovados_pagina = 0
            
            for numero in numeros_unicos_pagina:
                aprovado, motivo, ano = aplicar_filtro_2010_plus(numero)
                ocorrencias_na_pagina = numeros_encontrados.count(numero)
                
                # Registrar detalhes
                detalhes_por_numero.append({
                    "PaginaPDF": pagina_display,
                    "NumeroAutos": numero,
                    "AnoExtraido": ano,
                    "StatusFiltro": "APROVADO" if aprovado else "REPROVADO",
                    "MotivoReprovacao": motivo if not aprovado else "",
                    "OcorrenciasNestaPagina": ocorrencias_na_pagina
                })
                
                # Contadores
                if aprovado:
                    aprovados_pagina += 1
                    contadores["total_aprovados"] += 1
                    numeros_aprovados_pagina.extend([numero] * ocorrencias_na_pagina)
                else:
                    reprovados_pagina += 1
                    contadores["total_reprovados"] += 1
                    motivos_reprovacao[motivo] += 1
                
                if ano:
                    anos_encontrados[ano] += 1
                
                contadores["total_numeros_unicos"] += 1
                contadores["total_ocorrencias"] += ocorrencias_na_pagina
            
            # Armazenar aprovados
            numeros_aprovados_por_pagina[pagina_display] = numeros_aprovados_pagina
            
            # Estatísticas da página
            estat_pagina = {
                "PaginaPDF": pagina_display,
                "TotalNumeros": len(numeros_encontrados),
                "NumerosUnicos": len(numeros_unicos_pagina),
                "Duplicatas": len(numeros_encontrados) - len(numeros_unicos_pagina),
                "Aprovados": aprovados_pagina,
                "Reprovados": reprovados_pagina,
                "PercentualAprovacao": (aprovados_pagina / len(numeros_unicos_pagina) * 100) if numeros_unicos_pagina else 0
            }
            estatisticas_por_pagina.append(estat_pagina)
            
        except Exception as e:
            print(f"  ❌ Erro na página {pagina_display}: {e}")
    
    doc_pdf.close()
    
    print(f"\n🔍 Processamento concluído. Analisando duplicados...")
    
    # Análise detalhada de duplicados nos números BRUTOS
    analise_brutos = analisar_duplicados_detalhado(numeros_brutos_por_pagina)
    
    # Análise detalhada de duplicados nos números APROVADOS  
    analise_aprovados = analisar_duplicados_detalhado(numeros_aprovados_por_pagina)
    
    # Calcular estatísticas finais
    taxa_aprovacao = (contadores["total_aprovados"] / contadores["total_numeros_unicos"] * 100) if contadores["total_numeros_unicos"] > 0 else 0
    
    # Números limpos finais (aprovados únicos)
    numeros_limpos = list(analise_aprovados['numeros_unicos'])
    
    print("\n" + "="*90)
    print("📊 RELATÓRIO DE DUPLICADOS - FILTRO 2010+")
    print("="*90)
    print(f"📄 Páginas processadas: {total_paginas - pagina_inicial + 1}")
    print(f"🔢 Total de ocorrências brutas: {analise_brutos['total_numeros_brutos']}")
    print(f"🆔 Números únicos brutos: {len(analise_brutos['contador_global'])}")
    print(f"📋 Duplicados únicos (brutos): {analise_brutos['total_duplicados_unicos']}")
    
    print(f"\n🎯 APÓS FILTRO 2010+:")
    print(f"✅ Números aprovados (únicos): {contadores['total_aprovados']}")
    print(f"❌ Números reprovados: {contadores['total_reprovados']}")
    print(f"📈 Taxa de aprovação: {taxa_aprovacao:.1f}%")
    
    print(f"\n🧹 NÚMEROS LIMPOS (Aprovados + Sem Duplicatas):")
    print(f"🎯 Total de números limpos: {len(numeros_limpos)}")
    print(f"📋 Duplicados nos aprovados: {analise_aprovados['total_duplicados_unicos']}")
    
    if analise_aprovados['total_duplicados_unicos'] > 0:
        print(f"\n🔄 TOP 5 DUPLICADOS (nos aprovados):")
        for i, dup in enumerate(analise_aprovados['numeros_duplicados'][:5]):
            print(f"  {i+1}. {dup['numero']} - {dup['total_ocorrencias']} ocorrências")
    
    # Preparar dados completos para retorno
    relatorio_completo = {
        "detalhes_por_numero": detalhes_por_numero,
        "estatisticas_por_pagina": estatisticas_por_pagina,
        "contadores_gerais": dict(contadores),
        "anos_encontrados": dict(anos_encontrados),
        "motivos_reprovacao": dict(motivos_reprovacao),
        "analise_brutos": analise_brutos,
        "analise_aprovados": analise_aprovados,
        "numeros_limpos": numeros_limpos,
        "taxa_aprovacao": taxa_aprovacao,
        "pagina_inicial_processada": pagina_inicial,
        "total_paginas_processadas": total_paginas - pagina_inicial + 1
    }
    
    return relatorio_completo

def gerar_relatorio_excel_duplicados(relatorio, nome_arquivo):
    """
    Gera relatório Excel focado em duplicados
    """
    print(f"\n💾 Gerando relatório de duplicados: {nome_arquivo}")
    
    try:
        with pd.ExcelWriter(nome_arquivo, engine='openpyxl') as writer:
            
            # Aba 1: Resumo Executivo
            resumo_data = [
                ["Métrica", "Valor"],
                ["Página Inicial Processada", relatorio["pagina_inicial_processada"]],
                ["Total de Páginas Processadas", relatorio["total_paginas_processadas"]],
                ["", ""],
                ["=== NÚMEROS BRUTOS ===", ""],
                ["Total de Ocorrências Brutas", relatorio["analise_brutos"]["total_numeros_brutos"]],
                ["Números Únicos (Brutos)", len(relatorio["analise_brutos"]["contador_global"])],
                ["Números com Duplicatas", relatorio["analise_brutos"]["total_duplicados_unicos"]],
                ["", ""],
                ["=== APÓS FILTRO 2010+ ===", ""],
                ["Números Aprovados", relatorio["contadores_gerais"]["total_aprovados"]],
                ["Números Reprovados", relatorio["contadores_gerais"]["total_reprovados"]],
                ["Taxa de Aprovação (%)", f"{relatorio['taxa_aprovacao']:.2f}"],
                ["", ""],
                ["=== NÚMEROS LIMPOS ===", ""],
                ["Total Números Limpos (Únicos + Aprovados)", len(relatorio["numeros_limpos"])],
                ["Duplicados nos Aprovados", relatorio["analise_aprovados"]["total_duplicados_unicos"]],
                ["Taxa de Limpeza (%)", f"{(len(relatorio['numeros_limpos']) / relatorio['contadores_gerais']['total_aprovados'] * 100):.2f}" if relatorio['contadores_gerais']['total_aprovados'] > 0 else "0.00"],
            ]
            df_resumo = pd.DataFrame(resumo_data[1:], columns=resumo_data[0])
            df_resumo.to_excel(writer, sheet_name='Resumo_Executivo', index=False)
            
            # Aba 2: Números Limpos (RESULTADO FINAL)
            if relatorio["numeros_limpos"]:
                df_limpos_data = []
                for numero in sorted(relatorio["numeros_limpos"]):
                    ano = extrair_ano_processo(numero)
                    df_limpos_data.append({
                        "NumeroAutos": numero,
                        "AnoExtraido": ano,
                        "Status": "LIMPO_PRONTO_USO"
                    })
                df_limpos = pd.DataFrame(df_limpos_data)
                df_limpos.to_excel(writer, sheet_name='Numeros_Limpos_Final', index=False)
            
            # Aba 3: Duplicados Brutos (Detalhado)
            if relatorio["analise_brutos"]["numeros_duplicados"]:
                df_dup_brutos_data = []
                for dup in relatorio["analise_brutos"]["numeros_duplicados"]:
                    df_dup_brutos_data.append({
                        "NumeroAutos": dup['numero'],
                        "TotalOcorrencias": dup['total_ocorrencias'],
                        "PaginasEncontradas": ", ".join(map(str, dup['paginas_encontradas'])),
                        "SpanPaginas": dup['span_paginas'],
                        "AnoExtraido": extrair_ano_processo(dup['numero'])
                    })
                df_dup_brutos = pd.DataFrame(df_dup_brutos_data)
                df_dup_brutos.to_excel(writer, sheet_name='Duplicados_Brutos', index=False)
            
            # Aba 4: Duplicados Aprovados (Detalhado)
            if relatorio["analise_aprovados"]["numeros_duplicados"]:
                df_dup_aprov_data = []
                for dup in relatorio["analise_aprovados"]["numeros_duplicados"]:
                    df_dup_aprov_data.append({
                        "NumeroAutos": dup['numero'],
                        "TotalOcorrencias": dup['total_ocorrencias'],
                        "PaginasEncontradas": ", ".join(map(str, dup['paginas_encontradas'])),
                        "SpanPaginas": dup['span_paginas'],
                        "AnoExtraido": extrair_ano_processo(dup['numero'])
                    })
                df_dup_aprov = pd.DataFrame(df_dup_aprov_data)
                df_dup_aprov.to_excel(writer, sheet_name='Duplicados_Aprovados', index=False)
            
            # Aba 5: Detalhes Completos por Número
            df_detalhes = pd.DataFrame(relatorio["detalhes_por_numero"])
            df_detalhes.to_excel(writer, sheet_name='Detalhes_Completos', index=False)
            
            # Aba 6: Estatísticas por Página
            df_paginas = pd.DataFrame(relatorio["estatisticas_por_pagina"])
            df_paginas.to_excel(writer, sheet_name='Estatisticas_por_Pagina', index=False)
            
            # Aba 7: Distribuição por Ano
            if relatorio["anos_encontrados"]:
                anos_data = [[ano, qtd] for ano, qtd in sorted(relatorio["anos_encontrados"].items())]
                df_anos = pd.DataFrame(anos_data, columns=["Ano", "Quantidade"])
                df_anos.to_excel(writer, sheet_name='Distribuicao_por_Ano', index=False)
            
            # Aba 8: Motivos de Reprovação
            if relatorio["motivos_reprovacao"]:
                motivos_data = [[motivo, qtd] for motivo, qtd in sorted(relatorio["motivos_reprovacao"].items())]
                df_motivos = pd.DataFrame(motivos_data, columns=["Motivo", "Quantidade"])
                df_motivos.to_excel(writer, sheet_name='Motivos_Reprovacao', index=False)
        
        print(f"✅ Relatório Excel gerado com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao gerar relatório Excel: {e}")
        return False

def gerar_arquivo_numeros_limpos(numeros_limpos, nome_arquivo):
    """
    Gera arquivo TXT simples com números limpos (um por linha)
    """
    try:
        with open(nome_arquivo, 'w', encoding='utf-8') as f:
            for numero in sorted(numeros_limpos):
                f.write(f"{numero}\n")
        print(f"✅ Lista de números limpos salva: {os.path.basename(nome_arquivo)}")
        return True
    except Exception as e:
        print(f"❌ Erro ao gerar arquivo de números limpos: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 VERIFICADOR DE DUPLICADOS TJSP - FILTRO 2010+")
    print("="*70)
    
    # Configurar diretórios
    configurar_diretorios()
    
    # Verificar se PDF existe
    if not os.path.exists(PDF_CAMINHO):
        print(f"❌ PDF não encontrado: {PDF_CAMINHO}")
        print("Por favor, certifique-se de que o arquivo 'pdf.pdf' está na pasta:")
        print(f"  {SCRIPT_DIR}")
        input("\nPressione Enter para sair...")
        return
    
    # Solicitar página inicial (padrão 18800)
    try:
        pagina_input = input(f"📄 Digite a página inicial (padrão: {PAGINA_INICIAL_PADRAO}): ").strip()
        if pagina_input and pagina_input.isdigit():
            pagina_inicial = int(pagina_input)
        else:
            pagina_inicial = PAGINA_INICIAL_PADRAO
            if pagina_input:
                print(f"⚠️ Entrada inválida, usando padrão {PAGINA_INICIAL_PADRAO}.")
    except:
        pagina_inicial = PAGINA_INICIAL_PADRAO
        print(f"⚠️ Erro na entrada, usando padrão {PAGINA_INICIAL_PADRAO}.")
    
    # Processar PDF
    relatorio = processar_pdf_duplicados(PDF_CAMINHO, pagina_inicial)
    
    if relatorio is None:
        print("❌ Falha no processamento do PDF.")
        input("\nPressione Enter para sair...")
        return
    
    # Gerar nomes dos arquivos de saída
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_excel = os.path.join(SCRIPT_DIR, f"Relatorio_Duplicados_2010Plus_{timestamp}.xlsx")
    nome_txt_limpos = os.path.join(SCRIPT_DIR, f"Numeros_Limpos_2010Plus_{timestamp}.txt")
    
    # Gerar relatórios
    sucesso_excel = gerar_relatorio_excel_duplicados(relatorio, nome_excel)
    sucesso_txt = gerar_arquivo_numeros_limpos(relatorio["numeros_limpos"], nome_txt_limpos)
    
    if sucesso_excel and sucesso_txt:
        print(f"\n📁 Arquivos gerados em: {SCRIPT_DIR}")
        print(f"📊 Relatório Excel: {os.path.basename(nome_excel)}")
        print(f"📋 Lista Limpa: {os.path.basename(nome_txt_limpos)}")
    
    # Resumo final
    print("\n" + "="*70)
    print("🎯 RESUMO FINAL")
    print("="*70)
    print(f"📄 Páginas processadas: {relatorio['total_paginas_processadas']}")
    print(f"🔢 Números únicos encontrados: {len(relatorio['analise_brutos']['contador_global'])}")
    print(f"✅ Números aprovados (filtro 2010+): {relatorio['contadores_gerais']['total_aprovados']}")
    print(f"🧹 Números limpos (sem duplicatas): {len(relatorio['numeros_limpos'])}")
    
    duplicados_aprovados = relatorio['analise_aprovados']['total_duplicados_unicos']
    if duplicados_aprovados > 0:
        print(f"⚠️  {duplicados_aprovados} números aprovados tinham duplicatas")
    else:
        print("✅ Nenhum duplicado encontrado nos números aprovados!")
    
    taxa_limpeza = (len(relatorio['numeros_limpos']) / relatorio['contadores_gerais']['total_aprovados'] * 100) if relatorio['contadores_gerais']['total_aprovados'] > 0 else 0
    print(f"📈 Taxa de limpeza: {taxa_limpeza:.1f}%")
    
    print(f"\n🎉 Lista limpa pronta para próximas etapas!")
    print(f"📋 Use o arquivo: {os.path.basename(nome_txt_limpos)}")
    
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
