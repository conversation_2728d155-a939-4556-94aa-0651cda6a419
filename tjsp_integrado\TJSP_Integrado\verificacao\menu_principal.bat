@echo off
title Menu Principal - Verificadores TJSP
color 0A

:MENU
cls
echo.
echo ================================================================================
echo                          VERIFICADORES TJSP - MENU PRINCIPAL
echo ================================================================================
echo.
echo Escolha qual verificacao deseja executar:
echo.
echo   [1] Verificador de Filtro TJSP (Anos 2001-2009)
echo       - Primeira etapa de filtragem
echo       - Analise completa de todos os criterios iniciais
echo       - Pagina inicial configuravel
echo.
echo   [2] Verificador de Duplicados (Anos 2010+, a partir da pagina 18800)
echo       - Foco na deteccao de duplicados
echo       - Filtro atualizado (ano ^> 2010, nao termina 0500)
echo       - Gera lista limpa para proximas etapas
echo.
echo   [3] Sair
echo.
echo ================================================================================
echo.
set /p choice="Digite sua opcao (1, 2 ou 3): "

if "%choice%"=="1" goto FILTRO_2001_2009
if "%choice%"=="2" goto DUPLICADOS_2010_PLUS
if "%choice%"=="3" goto SAIR
goto OPCAO_INVALIDA

:FILTRO_2001_2009
cls
echo.
echo ========================================
echo   VERIFICADOR DE FILTRO TJSP
echo   (Anos 2001-2009)
echo ========================================
echo.
echo Executando verificador de filtro inicial...
echo.
call executar.bat
goto FIM

:DUPLICADOS_2010_PLUS
cls
echo.
echo ========================================
echo   VERIFICADOR DE DUPLICADOS TJSP
echo   (Anos 2010+, Pagina 18800+)
echo ========================================
echo.
echo Executando verificador de duplicados...
echo.
call executar_duplicados_2010plus.bat
goto FIM

:OPCAO_INVALIDA
cls
echo.
echo OPCAO INVALIDA! Por favor, escolha 1, 2 ou 3.
echo.
pause
goto MENU

:SAIR
cls
echo.
echo Saindo do sistema de verificadores TJSP...
echo.
echo Obrigado por usar os verificadores TJSP!
echo.
timeout /t 2 >nul
exit

:FIM
echo.
echo ================================================================================
echo.
echo Operacao concluida!
echo.
echo Deseja executar outro verificador?
echo.
set /p continuar="Digite S para voltar ao menu ou qualquer tecla para sair: "
if /i "%continuar%"=="s" goto MENU
if /i "%continuar%"=="sim" goto MENU
goto SAIR
