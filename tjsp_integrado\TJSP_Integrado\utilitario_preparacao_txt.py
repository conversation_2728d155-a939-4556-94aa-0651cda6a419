#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Utilitário para Preparação de Arquivo TXT - TJSP Completo
Auxilia na preparação, validação e conversão de arquivos TXT para o processador TJSP.
"""

import os
import re
import sys
from datetime import datetime

def validar_numero_autos(numero):
    """Valida se o número de autos está no formato correto"""
    padrao = r'^\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4}$'
    return re.match(padrao, numero.strip()) is not None

def extrair_ano_numero(numero):
    """Extrai o ano do número de autos"""
    match = re.search(r'\d{7}-\d{2}\.(\d{4})\.\d\.\d{2}\.\d{4}', numero)
    return int(match.group(1)) if match else None

def analisar_arquivo_txt(caminho_arquivo):
    """Analisa um arquivo TXT e retorna estatísticas"""
    if not os.path.exists(caminho_arquivo):
        return None
    
    stats = {
        'total_linhas': 0,
        'linhas_vazias': 0,
        'numeros_validos': 0,
        'numeros_invalidos': 0,
        'duplicatas': 0,
        'anos_encontrados': {},
        'terminados_0500': 0,
        'numeros_unicos': set(),
        'problemas': []
    }
    
    try:
        with open(caminho_arquivo, 'r', encoding='utf-8') as f:
            for linha_num, linha in enumerate(f, 1):
                stats['total_linhas'] += 1
                numero = linha.strip()
                
                if not numero:
                    stats['linhas_vazias'] += 1
                    continue
                
                if validar_numero_autos(numero):
                    stats['numeros_validos'] += 1
                    
                    # Verificar duplicatas
                    if numero in stats['numeros_unicos']:
                        stats['duplicatas'] += 1
                    else:
                        stats['numeros_unicos'].add(numero)
                    
                    # Extrair ano
                    ano = extrair_ano_numero(numero)
                    if ano:
                        stats['anos_encontrados'][ano] = stats['anos_encontrados'].get(ano, 0) + 1
                    
                    # Verificar terminação 0500
                    if numero.endswith('0500'):
                        stats['terminados_0500'] += 1
                else:
                    stats['numeros_invalidos'] += 1
                    stats['problemas'].append(f"Linha {linha_num}: formato inválido '{numero}'")
        
        return stats
    except Exception as e:
        print(f"❌ Erro ao ler arquivo: {e}")
        return None

def limpar_arquivo_txt(caminho_entrada, caminho_saida=None):
    """Limpa um arquivo TXT removendo duplicatas e números inválidos"""
    if not caminho_saida:
        nome_base = os.path.splitext(caminho_entrada)[0]
        caminho_saida = f"{nome_base}_limpo.txt"
    
    numeros_unicos = set()
    numeros_validos = []
    
    with open(caminho_entrada, 'r', encoding='utf-8') as f:
        for linha_num, linha in enumerate(f, 1):
            numero = linha.strip()
            if not numero:
                continue
            
            if validar_numero_autos(numero):
                if numero not in numeros_unicos:
                    numeros_unicos.add(numero)
                    numeros_validos.append(numero)
    
    # Ordenar números
    numeros_validos.sort()
    
    with open(caminho_saida, 'w', encoding='utf-8') as f:
        for numero in numeros_validos:
            f.write(f"{numero}\n")
    
    return caminho_saida, len(numeros_validos)

def converter_excel_para_txt(caminho_excel, coluna_numeros, caminho_saida=None):
    """Converte uma coluna de Excel para arquivo TXT"""
    try:
        import pandas as pd
        
        df = pd.read_excel(caminho_excel)
        
        if coluna_numeros not in df.columns:
            print(f"❌ Coluna '{coluna_numeros}' não encontrada no Excel")
            print(f"Colunas disponíveis: {list(df.columns)}")
            return None
        
        numeros = df[coluna_numeros].dropna().astype(str).tolist()
        
        if not caminho_saida:
            nome_base = os.path.splitext(caminho_excel)[0]
            caminho_saida = f"{nome_base}_numeros.txt"
        
        numeros_validos = []
        for numero in numeros:
            numero = numero.strip()
            if validar_numero_autos(numero):
                numeros_validos.append(numero)
        
        # Remover duplicatas e ordenar
        numeros_unicos = sorted(list(set(numeros_validos)))
        
        with open(caminho_saida, 'w', encoding='utf-8') as f:
            for numero in numeros_unicos:
                f.write(f"{numero}\n")
        
        return caminho_saida, len(numeros_unicos)
        
    except ImportError:
        print("❌ Biblioteca pandas não encontrada. Execute: pip install pandas openpyxl")
        return None
    except Exception as e:
        print(f"❌ Erro ao converter Excel: {e}")
        return None

def renomear_para_autosfiltrados(caminho_origem):
    """Renomeia um arquivo para autosfiltrados.txt"""
    diretorio = os.path.dirname(caminho_origem)
    destino = os.path.join(diretorio, "autosfiltrados.txt")
    
    if os.path.exists(destino):
        backup = f"autosfiltrados_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        backup_path = os.path.join(diretorio, backup)
        os.rename(destino, backup_path)
        print(f"📁 Backup criado: {backup}")
    
    os.rename(caminho_origem, destino)
    return destino

def exibir_relatorio_arquivo(stats):
    """Exibe relatório detalhado de um arquivo TXT"""
    print("\n" + "="*60)
    print("📊 RELATÓRIO DO ARQUIVO TXT")
    print("="*60)
    print(f"📄 Total de linhas: {stats['total_linhas']}")
    print(f"📝 Linhas vazias: {stats['linhas_vazias']}")
    print(f"✅ Números válidos: {stats['numeros_validos']}")
    print(f"❌ Números inválidos: {stats['numeros_invalidos']}")
    print(f"🔄 Duplicatas encontradas: {stats['duplicatas']}")
    print(f"🧹 Números únicos: {len(stats['numeros_unicos'])}")
    print(f"🚫 Terminados em 0500: {stats['terminados_0500']}")
    
    if stats['anos_encontrados']:
        print(f"\n📅 DISTRIBUIÇÃO POR ANO:")
        for ano in sorted(stats['anos_encontrados'].keys()):
            print(f"  {ano}: {stats['anos_encontrados'][ano]} números")
    
    if stats['problemas']:
        print(f"\n🚨 PROBLEMAS ENCONTRADOS (primeiros 10):")
        for problema in stats['problemas'][:10]:
            print(f"  {problema}")
        if len(stats['problemas']) > 10:
            print(f"  ... e mais {len(stats['problemas']) - 10} problemas")

def menu_principal():
    """Menu principal do utilitário"""
    while True:
        print("\n" + "="*60)
        print("🛠️ UTILITÁRIO DE PREPARAÇÃO - TJSP COMPLETO")
        print("="*60)
        print("\nEscolha uma opção:")
        print("\n1. 📊 Analisar arquivo TXT existente")
        print("2. 🧹 Limpar arquivo TXT (remover duplicatas/inválidos)")  
        print("3. 📋 Converter Excel para TXT")
        print("4. ✏️ Renomear arquivo para 'autosfiltrados.txt'")
        print("5. 📁 Verificar arquivo 'autosfiltrados.txt' atual")
        print("6. ❌ Sair")
        
        opcao = input(f"\nDigite sua opção (1-6): ").strip()
        
        if opcao == "1":
            analisar_arquivo_interativo()
        elif opcao == "2":
            limpar_arquivo_interativo()
        elif opcao == "3":
            converter_excel_interativo()
        elif opcao == "4":
            renomear_arquivo_interativo()
        elif opcao == "5":
            verificar_autosfiltrados()
        elif opcao == "6":
            print("\n👋 Saindo do utilitário...")
            break
        else:
            print("\n❌ Opção inválida. Tente novamente.")

def analisar_arquivo_interativo():
    """Análise interativa de arquivo TXT"""
    caminho = input("\n📁 Digite o caminho do arquivo TXT: ").strip().strip('"')
    
    if not os.path.exists(caminho):
        print(f"❌ Arquivo não encontrado: {caminho}")
        return
    
    print(f"\n🔍 Analisando arquivo: {os.path.basename(caminho)}")
    stats = analisar_arquivo_txt(caminho)
    
    if stats:
        exibir_relatorio_arquivo(stats)
    else:
        print("❌ Falha na análise do arquivo.")

def limpar_arquivo_interativo():
    """Limpeza interativa de arquivo TXT"""
    caminho = input("\n📁 Digite o caminho do arquivo TXT para limpar: ").strip().strip('"')
    
    if not os.path.exists(caminho):
        print(f"❌ Arquivo não encontrado: {caminho}")
        return
    
    print(f"\n🧹 Limpando arquivo: {os.path.basename(caminho)}")
    
    try:
        caminho_limpo, total_validos = limpar_arquivo_txt(caminho)
        print(f"✅ Arquivo limpo criado: {caminho_limpo}")
        print(f"📊 Total de números válidos únicos: {total_validos}")
        
        renomear = input(f"\nDeseja renomear para 'autosfiltrados.txt'? (s/n): ").strip().lower()
        if renomear == 's':
            destino = renomear_para_autosfiltrados(caminho_limpo)
            print(f"✅ Arquivo renomeado para: {destino}")
            
    except Exception as e:
        print(f"❌ Erro na limpeza: {e}")

def converter_excel_interativo():
    """Conversão interativa de Excel para TXT"""
    caminho_excel = input("\n📁 Digite o caminho do arquivo Excel: ").strip().strip('"')
    
    if not os.path.exists(caminho_excel):
        print(f"❌ Arquivo não encontrado: {caminho_excel}")
        return
    
    coluna = input("📋 Digite o nome da coluna com os números de autos: ").strip()
    
    print(f"\n🔄 Convertendo Excel para TXT...")
    
    resultado = converter_excel_para_txt(caminho_excel, coluna)
    if resultado:
        caminho_txt, total = resultado
        print(f"✅ Conversão concluída: {caminho_txt}")
        print(f"📊 Total de números únicos válidos: {total}")
        
        renomear = input(f"\nDeseja renomear para 'autosfiltrados.txt'? (s/n): ").strip().lower()
        if renomear == 's':
            destino = renomear_para_autosfiltrados(caminho_txt)
            print(f"✅ Arquivo renomeado para: {destino}")

def renomear_arquivo_interativo():
    """Renomeação interativa de arquivo"""
    caminho = input("\n📁 Digite o caminho do arquivo para renomear: ").strip().strip('"')
    
    if not os.path.exists(caminho):
        print(f"❌ Arquivo não encontrado: {caminho}")
        return
    
    try:
        destino = renomear_para_autosfiltrados(caminho)
        print(f"✅ Arquivo renomeado para: {destino}")
    except Exception as e:
        print(f"❌ Erro ao renomear: {e}")

def verificar_autosfiltrados():
    """Verifica o arquivo autosfiltrados.txt atual"""
    caminho = "autosfiltrados.txt"
    
    if not os.path.exists(caminho):
        print(f"\n❌ Arquivo 'autosfiltrados.txt' não encontrado no diretório atual.")
        print(f"📁 Diretório atual: {os.getcwd()}")
        return
    
    print(f"\n🔍 Verificando arquivo 'autosfiltrados.txt'...")
    stats = analisar_arquivo_txt(caminho)
    
    if stats:
        exibir_relatorio_arquivo(stats)
        
        if stats['numeros_invalidos'] > 0 or stats['duplicatas'] > 0:
            print(f"\n⚠️ Problemas detectados no arquivo!")
            limpar = input(f"Deseja criar uma versão limpa? (s/n): ").strip().lower()
            if limpar == 's':
                limpar_arquivo_txt(caminho, "autosfiltrados_limpo.txt")
                print(f"✅ Versão limpa criada: autosfiltrados_limpo.txt")
    else:
        print("❌ Falha na verificação do arquivo.")

if __name__ == "__main__":
    print("🛠️ UTILITÁRIO DE PREPARAÇÃO - TJSP COMPLETO")
    print("Versão TXT Filtrado")
    print("-" * 50)
    
    try:
        menu_principal()
    except KeyboardInterrupt:
        print(f"\n\n👋 Utilitário interrompido pelo usuário.")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
    
    input(f"\nPressione Enter para sair...")
