#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Verificador de Filtro TJSP - Primeira Etapa
Analisa PDF e aplica filtros iniciais (ano 2001-2009 e exclusão de terminados em 0500)
Gera relatório detalhado com mapeamento completo e estatísticas de perda de dados.
"""

import os
import sys
import re
import pandas as pd
import fitz  # PyMuPDF
from datetime import datetime
from collections import defaultdict

# --- Configurações ---
SCRIPT_DIR = r"C:\Users\<USER>\OneDrive\Documentos\VS Code\0 - Automação Completa\TJSP_Integrado\verificacao"
PDF_NOME = "pdf.pdf"
PDF_CAMINHO = os.path.join(SCRIPT_DIR, PDF_NOME)

def configurar_diretorios():
    """Cria o diretório se não existir"""
    if not os.path.exists(SCRIPT_DIR):
        os.makedirs(SCRIPT_DIR, exist_ok=True)
        print(f"📁 Diretório criado: {SCRIPT_DIR}")

def extrair_ano_processo(numero_autos):
    """Extrai o ano do número de autos"""
    match = re.search(r'\d{7}-\d{2}\.(\d{4})\.\d\.\d{2}\.\d{4}', numero_autos)
    return int(match.group(1)) if match else None

def classificar_numero_autos(numero_autos):
    """
    Classifica o número de autos segundo os critérios de filtro
    Retorna: (aprovado, motivo_reprovacao, ano_extraido)
    """
    # Extrair ano
    ano = extrair_ano_processo(numero_autos)
    
    if ano is None:
        return False, "FORMATO_INVALIDO", None
    
    # Verificar critério de ano (2001-2009)
    if not (2001 <= ano <= 2009):
        return False, f"ANO_FORA_CRITERIO_{ano}", ano
    
    # Verificar se termina com 0500
    if numero_autos.endswith('0500'):
        return False, "TERMINA_COM_0500", ano
    
    return True, "APROVADO", ano

def processar_pdf_completo(caminho_pdf, pagina_inicial=1):
    """
    Processa todo o PDF aplicando os filtros e coletando estatísticas detalhadas
    """
    print(f"🚀 Iniciando verificação do PDF: {os.path.basename(caminho_pdf)}")
    print(f"📍 Caminho: {caminho_pdf}")
    
    if not os.path.exists(caminho_pdf):
        print(f"❌ Arquivo PDF não encontrado: {caminho_pdf}")
        return None
    
    # Abrir PDF
    try:
        doc_pdf = fitz.open(caminho_pdf)
        total_paginas = len(doc_pdf)
        print(f"📚 PDF aberto com sucesso. Total de páginas: {total_paginas}")
    except Exception as e:
        print(f"❌ Erro ao abrir PDF: {e}")
        return None
    
    # Validar página inicial
    if not (1 <= pagina_inicial <= total_paginas):
        print(f"⚠️ Página inicial ({pagina_inicial}) inválida. Usando página 1.")
        pagina_inicial = 1
    
    # Estruturas para coleta de dados
    resultados_detalhados = []
    estatisticas_por_pagina = []
    contadores_gerais = defaultdict(int)
    anos_encontrados = defaultdict(int)
    motivos_reprovacao = defaultdict(int)
    
    print(f"\n🔍 Iniciando análise da página {pagina_inicial} até {total_paginas}...")
    print("="*80)
    
    # Processar cada página
    for num_pagina in range(pagina_inicial - 1, total_paginas):
        pagina_display = num_pagina + 1
        print(f"\n📄 Processando página {pagina_display}/{total_paginas}")
        
        try:
            # Carregar página e extrair texto
            pagina = doc_pdf.load_page(num_pagina)
            texto_pagina = pagina.get_text("text")
            
            # Encontrar todos os números de autos na página
            numeros_encontrados = re.findall(r'\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4}', texto_pagina)
            numeros_unicos = list(set(numeros_encontrados))  # Remover duplicatas
            
            # Estatísticas da página
            total_numeros_pagina = len(numeros_encontrados)
            unicos_pagina = len(numeros_unicos)
            duplicatas_pagina = total_numeros_pagina - unicos_pagina
            
            aprovados_pagina = 0
            reprovados_pagina = 0
            
            print(f"  📊 Números encontrados: {total_numeros_pagina} (únicos: {unicos_pagina}, duplicatas: {duplicatas_pagina})")
            
            # Analisar cada número único
            for numero in numeros_unicos:
                aprovado, motivo, ano = classificar_numero_autos(numero)
                
                # Registrar resultado detalhado
                resultado = {
                    "PaginaPDF": pagina_display,
                    "NumeroAutos": numero,
                    "AnoExtraido": ano,
                    "StatusFiltro": "APROVADO" if aprovado else "REPROVADO",
                    "MotivoReprovacao": motivo if not aprovado else "",
                    "OcorrenciasNaPagina": numeros_encontrados.count(numero)
                }
                resultados_detalhados.append(resultado)
                
                # Atualizar contadores
                contadores_gerais["total_numeros_unicos"] += 1
                contadores_gerais["total_ocorrencias"] += numeros_encontrados.count(numero)
                
                if aprovado:
                    aprovados_pagina += 1
                    contadores_gerais["aprovados"] += 1
                else:
                    reprovados_pagina += 1
                    contadores_gerais["reprovados"] += 1
                    motivos_reprovacao[motivo] += 1
                
                # Contar anos
                if ano:
                    anos_encontrados[ano] += 1
            
            # Estatísticas da página
            estat_pagina = {
                "PaginaPDF": pagina_display,
                "TotalNumeros": total_numeros_pagina,
                "NumerosUnicos": unicos_pagina,
                "Duplicatas": duplicatas_pagina,
                "Aprovados": aprovados_pagina,
                "Reprovados": reprovados_pagina,
                "PercentualAprovacao": (aprovados_pagina / unicos_pagina * 100) if unicos_pagina > 0 else 0
            }
            estatisticas_por_pagina.append(estat_pagina)
            
            print(f"  ✅ Aprovados: {aprovados_pagina} | ❌ Reprovados: {reprovados_pagina}")
            
        except Exception as e:
            print(f"  ❌ Erro ao processar página {pagina_display}: {e}")
    
    doc_pdf.close()
    
    # Calcular estatísticas finais
    taxa_aprovacao = (contadores_gerais["aprovados"] / contadores_gerais["total_numeros_unicos"] * 100) if contadores_gerais["total_numeros_unicos"] > 0 else 0
    taxa_perda = 100 - taxa_aprovacao
    
    print("\n" + "="*80)
    print("📊 ESTATÍSTICAS FINAIS")
    print("="*80)
    print(f"📄 Páginas processadas: {total_paginas - pagina_inicial + 1}")
    print(f"🔢 Total de ocorrências encontradas: {contadores_gerais['total_ocorrencias']}")
    print(f"🆔 Números únicos encontrados: {contadores_gerais['total_numeros_unicos']}")
    print(f"✅ Números aprovados: {contadores_gerais['aprovados']} ({taxa_aprovacao:.1f}%)")
    print(f"❌ Números reprovados: {contadores_gerais['reprovados']} ({taxa_perda:.1f}%)")
    print(f"📉 Taxa de perda de dados: {taxa_perda:.1f}%")
    
    print(f"\n📅 DISTRIBUIÇÃO POR ANO:")
    for ano in sorted(anos_encontrados.keys()):
        print(f"  {ano}: {anos_encontrados[ano]} números")
    
    print(f"\n🚫 MOTIVOS DE REPROVAÇÃO:")
    for motivo, qtd in sorted(motivos_reprovacao.items()):
        print(f"  {motivo}: {qtd} números")
    
    # Preparar dados para retorno
    relatorio_completo = {
        "resultados_detalhados": resultados_detalhados,
        "estatisticas_por_pagina": estatisticas_por_pagina,
        "contadores_gerais": dict(contadores_gerais),
        "anos_encontrados": dict(anos_encontrados),
        "motivos_reprovacao": dict(motivos_reprovacao),
        "taxa_aprovacao": taxa_aprovacao,
        "taxa_perda": taxa_perda,
        "total_paginas_processadas": total_paginas - pagina_inicial + 1
    }
    
    return relatorio_completo

def gerar_relatorio_excel(relatorio, nome_arquivo):
    """
    Gera relatório completo em Excel com múltiplas abas
    """
    print(f"\n💾 Gerando relatório Excel: {nome_arquivo}")
    
    try:
        with pd.ExcelWriter(nome_arquivo, engine='openpyxl') as writer:
            # Aba 1: Resultados Detalhados
            df_detalhados = pd.DataFrame(relatorio["resultados_detalhados"])
            df_detalhados.to_excel(writer, sheet_name='Resultados_Detalhados', index=False)
            
            # Aba 2: Estatísticas por Página
            df_paginas = pd.DataFrame(relatorio["estatisticas_por_pagina"])
            df_paginas.to_excel(writer, sheet_name='Estatisticas_por_Pagina', index=False)
            
            # Aba 3: Resumo Geral
            resumo_data = [
                ["Métrica", "Valor"],
                ["Total de Páginas Processadas", relatorio["total_paginas_processadas"]],
                ["Total de Ocorrências", relatorio["contadores_gerais"]["total_ocorrencias"]],
                ["Números Únicos Encontrados", relatorio["contadores_gerais"]["total_numeros_unicos"]],
                ["Números Aprovados", relatorio["contadores_gerais"]["aprovados"]],
                ["Números Reprovados", relatorio["contadores_gerais"]["reprovados"]],
                ["Taxa de Aprovação (%)", f"{relatorio['taxa_aprovacao']:.2f}"],
                ["Taxa de Perda de Dados (%)", f"{relatorio['taxa_perda']:.2f}"],
            ]
            df_resumo = pd.DataFrame(resumo_data[1:], columns=resumo_data[0])
            df_resumo.to_excel(writer, sheet_name='Resumo_Geral', index=False)
            
            # Aba 4: Distribuição por Ano
            anos_data = [[ano, qtd] for ano, qtd in sorted(relatorio["anos_encontrados"].items())]
            if anos_data:
                df_anos = pd.DataFrame(anos_data, columns=["Ano", "Quantidade"])
                df_anos.to_excel(writer, sheet_name='Distribuicao_por_Ano', index=False)
            
            # Aba 5: Motivos de Reprovação
            motivos_data = [[motivo, qtd] for motivo, qtd in sorted(relatorio["motivos_reprovacao"].items())]
            if motivos_data:
                df_motivos = pd.DataFrame(motivos_data, columns=["Motivo", "Quantidade"])
                df_motivos.to_excel(writer, sheet_name='Motivos_Reprovacao', index=False)
            
            # Aba 6: Números Aprovados (Filtrados)
            aprovados = [r for r in relatorio["resultados_detalhados"] if r["StatusFiltro"] == "APROVADO"]
            if aprovados:
                df_aprovados = pd.DataFrame(aprovados)
                df_aprovados.to_excel(writer, sheet_name='Numeros_Aprovados', index=False)
        
        print(f"✅ Relatório Excel gerado com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao gerar relatório Excel: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 VERIFICADOR DE FILTRO TJSP - PRIMEIRA ETAPA")
    print("="*60)
    
    # Configurar diretórios
    configurar_diretorios()
    
    # Verificar se PDF existe
    if not os.path.exists(PDF_CAMINHO):
        print(f"❌ PDF não encontrado: {PDF_CAMINHO}")
        print("Por favor, certifique-se de que o arquivo 'pdf.pdf' está na pasta:")
        print(f"  {SCRIPT_DIR}")
        input("\nPressione Enter para sair...")
        return
    
    # Solicitar página inicial
    try:
        pagina_inicial_input = input("📄 Digite a página inicial para começar a análise (padrão: 1): ").strip()
        if pagina_inicial_input and pagina_inicial_input.isdigit():
            pagina_inicial = int(pagina_inicial_input)
        else:
            pagina_inicial = 1
            if pagina_inicial_input:
                print("⚠️ Entrada inválida, usando página 1.")
    except:
        pagina_inicial = 1
        print("⚠️ Erro na entrada, usando página 1.")
    
    # Processar PDF
    relatorio = processar_pdf_completo(PDF_CAMINHO, pagina_inicial)
    
    if relatorio is None:
        print("❌ Falha no processamento do PDF.")
        input("\nPressione Enter para sair...")
        return
    
    # Gerar nome do arquivo de saída
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_excel = os.path.join(SCRIPT_DIR, f"Relatorio_Filtro_TJSP_{timestamp}.xlsx")
    
    # Gerar relatório Excel
    sucesso_excel = gerar_relatorio_excel(relatorio, nome_excel)
    
    if sucesso_excel:
        print(f"\n📁 Arquivos gerados em: {SCRIPT_DIR}")
        print(f"📊 Relatório Excel: {os.path.basename(nome_excel)}")
    
    # Mostrar resumo final
    print("\n" + "="*60)
    print("🎯 RESUMO EXECUTIVO")
    print("="*60)
    print(f"Total de números únicos analisados: {relatorio['contadores_gerais']['total_numeros_unicos']}")
    print(f"Números que passaram no filtro: {relatorio['contadores_gerais']['aprovados']}")
    print(f"Taxa de aprovação: {relatorio['taxa_aprovacao']:.1f}%")
    print(f"Taxa de perda de dados: {relatorio['taxa_perda']:.1f}%")
    
    if relatorio['taxa_perda'] > 50:
        print("⚠️  ATENÇÃO: Alta taxa de perda de dados!")
    elif relatorio['taxa_perda'] > 25:
        print("⚠️  Moderada perda de dados.")
    else:
        print("✅ Baixa perda de dados.")
    
    print("\n🏁 Análise concluída!")
    input("\nPressione Enter para sair...")

if __name__ == "__main__":
    main()
