# PESQUISA DE SOLUÇÕES TÉCNICAS - TJSP AUTOMATION

## 📋 FUNDAMENTAÇÃO TÉCNICA

**Data:** 28/06/2025  
**Fontes:** Context7, <PERSON>it<PERSON><PERSON>, Stack Overflow, Selenium Documentation  
**Objetivo:** Soluções para erros críticos identificados na execução TJSP  

## 🔍 SOLUÇÕES IDENTIFICADAS

### 1. CORREÇÃO DO CAMINHO DO CERTIFICADO

**Problema:** Typo no caminho do certificado (`TJSP_Integraddo` vs `TJSP_Integrado`)  
**Solução:** Correção simples de string no código Python  

```python
# ❌ ERRO ATUAL
certificado_path = "C:\\Users\\<USER>\\Documents\\augment-projects\\MontSamm\\tjsp_integrado\\TJSP_Integraddo\\..\\Certificado - 41784463809 - DENIS HENRIQUE SOUSA OLIVEIRA (1).pfx"

# ✅ CORREÇÃO
certificado_path = "C:\\Users\\<USER>\\Documents\\augment-projects\\MontSamm\\tjsp_integrado\\TJSP_Integrado\\..\\Certificado - 41784463809 - DENIS HENRIQUE SOUSA OLIVEIRA (1).pfx"
```

### 2. INSTALAÇÃO AUTOMÁTICA DE EXTENSÃO CHROME

**Fonte:** Stack Overflow + Selenium Documentation  
**Método:** `add_extension()` do ChromeOptions  

#### Solução A: Extensão Empacotada (.crx)
```python
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

options = Options()
# Adicionar extensão WebSigner empacotada
options.add_extension('path/to/websigner.crx')
driver = webdriver.Chrome(options=options)
```

#### Solução B: Extensão Desempacotada (pasta)
```python
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

options = Options()
# Adicionar extensão WebSigner desempacotada
options.add_argument('--load-extension=/path/to/unpacked/extension')
driver = webdriver.Chrome(options=options)
```

### 3. CONFIGURAÇÃO DE PERFIL CHROME PERSISTENTE

**Fonte:** Selenium Chrome Documentation  
**Método:** `--user-data-dir` argument  

```python
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

options = Options()
# Usar perfil persistente específico para TJSP
options.add_argument('--user-data-dir=C:\\Users\\<USER>\\ChromeProfile_TJSP_Persistent')
options.add_argument('--profile-directory=Default')
# Manter extensões instaladas
options.add_argument('--disable-extensions-except=websigner-extension-id')
driver = webdriver.Chrome(options=options)
```

### 4. DETECÇÃO E CONFIGURAÇÃO WEBSIGNER

**Fonte:** Extension Bus Library (Context7)  
**Método:** Comunicação com extensão via JavaScript  

```python
# Verificar se WebSigner está carregado
def verificar_websigner(driver):
    try:
        # Executar JavaScript para detectar WebSigner
        websigner_status = driver.execute_script("""
            return typeof window.WebSigner !== 'undefined' && 
                   typeof window.WebSigner.selectCertificate === 'function';
        """)
        return websigner_status
    except Exception as e:
        return False

# Aguardar carregamento da extensão
def aguardar_websigner(driver, timeout=30):
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    
    def websigner_loaded(driver):
        return verificar_websigner(driver)
    
    WebDriverWait(driver, timeout).until(websigner_loaded)
```

## 🛠️ IMPLEMENTAÇÃO RECOMENDADA

### Estratégia 1: Perfil Persistente + Extensão Manual (RÁPIDA)
```python
def configurar_chrome_websigner():
    options = Options()
    
    # Perfil persistente para manter extensões
    profile_path = f"C:\\Users\\<USER>\\ChromeProfile_TJSP"
    options.add_argument(f'--user-data-dir={profile_path}')
    
    # Configurações de segurança para certificados
    options.add_argument('--allow-running-insecure-content')
    options.add_argument('--disable-web-security')
    options.add_argument('--ignore-certificate-errors')
    
    return options
```

### Estratégia 2: Instalação Automática + Verificação (ROBUSTA)
```python
def configurar_chrome_com_websigner():
    options = Options()
    
    # Tentar adicionar extensão WebSigner se disponível
    websigner_crx = "path/to/websigner.crx"
    if os.path.exists(websigner_crx):
        options.add_extension(websigner_crx)
    else:
        # Fallback para perfil persistente
        profile_path = f"C:\\Users\\<USER>\\ChromeProfile_TJSP"
        options.add_argument(f'--user-data-dir={profile_path}')
    
    # Configurações adicionais
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    
    return options
```

## 📚 REFERÊNCIAS TÉCNICAS

### Stack Overflow Solutions:
1. **Extension Loading:** "How to load extension within chrome driver in selenium with python"
   - Método `add_extension()` para arquivos .crx
   - Argumento `--load-extension` para pastas desempacotadas

2. **Chrome Options:** "Using Extensions with Selenium (Python)"
   - Configuração de ChromeOptions
   - Gerenciamento de perfis de usuário

### Selenium Documentation:
1. **Chrome Specific Functionality**
   - Extensions parameter accepts .crx files
   - Use `--load-extension` for unpacked directories

2. **ChromeOptions API**
   - `add_extension(extension: str)` method
   - User data directory configuration

### Context7 Libraries:
1. **Extension Bus** (/davestewart/extension-bus)
   - Universal message bus for Chrome extensions
   - Communication between extension and web page

2. **Chrome Extension MCP** (/tesla0225/chromeextension)
   - MCP server for Chrome extension automation
   - DOM manipulation and extension control

## 🎯 PRÓXIMAS AÇÕES RECOMENDADAS

### Prioridade ALTA:
1. **Correção imediata do typo no caminho do certificado**
2. **Implementação de perfil Chrome persistente**
3. **Teste de detecção automática do WebSigner**

### Prioridade MÉDIA:
1. **Obtenção do arquivo .crx do WebSigner**
2. **Implementação de instalação automática de extensão**
3. **Criação de sistema de fallback robusto**

### Prioridade BAIXA:
1. **Integração com Extension Bus para comunicação avançada**
2. **Implementação de MCP Chrome Extension Server**
3. **Monitoramento automático de status da extensão**

---
**Status:** Pesquisa técnica completa ✅  
**Próximo passo:** Geração de relatório de análise completa
