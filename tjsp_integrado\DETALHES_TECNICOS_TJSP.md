# 🔧 DETALHES TÉCNICOS - Sistema TJSP Integrado
## Especificações Técnicas e Arquitetura Detalhada

**Data**: 28/06/2025  
**Complemento**: MAPEAMENTO_COMPLETO_TJSP_SISTEMA.md  
**Foco**: Implementação técnica e integrações  

---

## 🏗️ ARQUITETURA TÉCNICA DETALHADA

### Diagrama de Componentes
```
┌─────────────────────────────────────────────────────────────┐
│                    TJSP INTEGRADO SYSTEM                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   ENTRADA   │  │ AUTENTICAÇÃO│  │  CONSULTA   │         │
│  │             │  │             │  │    TJSP     │         │
│  │autosfiltrados│  │ Certificado │  │             │         │
│  │.txt (11.117)│──│   Digital   │──│ Selenium    │         │
│  │             │  │ WebSigner   │  │ Chrome      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│           │                                │                │
│           ▼                                ▼                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  DOWNLOAD   │  │PROCESSAMENTO│  │ARMAZENAMENTO│         │
│  │    PDFs     │  │     IA      │  │   DADOS     │         │
│  │             │  │             │  │             │         │
│  │ Fallbacks   │  │    N8N      │  │Google Sheets│         │
│  │ Shadow DOM  │──│ GPT-4o-mini │──│Google Drive │         │
│  │ 1000+ docs  │  │ Extraction  │  │ Organized   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│           │                                │                │
│           ▼                                ▼                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │VERIFICAÇÃO  │  │ INTEGRAÇÃO  │  │   OUTPUTS   │         │
│  │   DADOS     │  │  EXTERNA    │  │   FINAIS    │         │
│  │             │  │             │  │             │         │
│  │ Duplicados  │  │   Receita   │  │Lista Limpa  │         │
│  │ Filtros     │  │  Federal    │  │ Relatórios  │         │
│  │ Validação   │  │ Power Auto  │  │ Excel/TXT   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

---

## 📋 ESPECIFICAÇÕES DOS COMPONENTES

### 1. SCRIPTS PYTHON PRINCIPAIS

#### **TJSP_completo.py** (929 linhas)
```python
# Funções principais identificadas:
- configurar_chrome_unificado()
- iniciar_navegador_unificado()
- fazer_login_websigner()
- consultar_processo_completo()
- processar_lista_completa()
```
**Responsabilidades**:
- Orquestração geral do sistema
- Gerenciamento de sessão Chrome
- Coordenação entre módulos
- Logging e monitoramento

#### **tjsp_download.py** (1163 linhas)
```python
# Estratégias de download implementadas:
- atualizar_baixar_documento() # Método principal
- download_via_iframe()        # Estratégia 1
- download_via_shadow_dom()    # Estratégia 2
- download_assistido_usuario() # Fallback
```
**Características técnicas**:
- Detecção automática de PDF viewers
- Navegação em Shadow DOM
- Timeouts configuráveis
- Recovery automático de erros

#### **utilitario_preparacao_txt.py** (330 linhas)
```python
# Funcionalidades implementadas:
- validar_numero_autos()       # Regex validation
- analisar_arquivo_txt()       # Statistical analysis
- limpar_arquivo_txt()         # Data cleaning
- converter_excel_para_txt()   # Format conversion
```
**Validação regex**: `r'^\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4}$'`

### 2. SISTEMA DE AUTENTICAÇÃO

#### **Certificado Digital**
- **Nome**: DENIS HENRIQUE SOUSA OLIVEIRA
- **CPF**: 41784463809
- **Arquivo**: `.pfx` format
- **Localização**: `tjsp_integrado/Certificado - 41784463809 - DENIS HENRIQUE SOUSA OLIVEIRA (1).pfx`

#### **WebSigner Integration**
- **Extensão**: WebSigner para Chrome
- **Detecção**: Automática via JavaScript
- **Fallback**: Processo manual documentado
- **Profile**: `C:/Users/<USER>/ClineAutomationProfile_TJSP`

### 3. WORKFLOW N8N DETALHADO

#### **Arquivo**: `Extracao_TJSP_Completo_Final.json` (3900 linhas)
```json
{
  "nodes": [
    {
      "name": "Google Drive Monitor",
      "type": "n8n-nodes-base.googleDrive",
      "folderId": "1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"
    },
    {
      "name": "OpenAI GPT-4o-mini",
      "type": "n8n-nodes-base.openAi",
      "model": "gpt-4o-mini"
    },
    {
      "name": "Google Sheets Output",
      "type": "n8n-nodes-base.googleSheets",
      "spreadsheetId": "17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw"
    }
  ]
}
```

#### **Prompt Engineering para IA**
```
Extrair dados estruturados de ofícios requisitórios:
- Número do processo
- Valor do débito
- Dados do devedor
- Classificação por faixa (BAIXO/MÉDIO/ALTO)
- Formato JSON estruturado
```

### 4. SISTEMA DE VERIFICAÇÃO

#### **Módulo 1**: Filtro 2001-2009
- **Script**: `verificador_filtro_tjsp.py`
- **Foco**: Análise histórica
- **Output**: 6 abas Excel

#### **Módulo 2**: Duplicados 2010+
- **Script**: `verificador_duplicados_2010plus.py`
- **Foco**: Lista limpa atual
- **Output**: 8 abas Excel + TXT limpo

#### **Menu Interativo**
```batch
# menu_principal.bat
echo "1. Executar Módulo 1 (Filtro 2001-2009)"
echo "2. Executar Módulo 2 (Duplicados 2010+)"
echo "3. Sair"
```

---

## 🔄 FLUXOS DE DADOS TÉCNICOS

### Pipeline de Processamento
```
INPUT: autosfiltrados.txt (11.117 processos)
  ↓
VALIDAÇÃO: Regex pattern matching
  ↓
AUTENTICAÇÃO: Certificado digital + WebSigner
  ↓
CONSULTA: Selenium automation + Chrome profile
  ↓
DOWNLOAD: Multi-strategy fallback system
  ↓
STORAGE: downloads_completos/ (1000+ PDFs)
  ↓
MONITORING: Google Drive folder watch
  ↓
AI PROCESSING: GPT-4o-mini structured extraction
  ↓
OUTPUT: Google Sheets organized by value ranges
  ↓
VERIFICATION: Duplicate detection + filtering
  ↓
FINAL: Clean list TXT + detailed Excel reports
```

### Integração Receita Federal
```
ENTRADA: CPF para validação
  ↓
NAVEGAÇÃO: NoDriver automation
  ↓
PREENCHIMENTO: JavaScript injection
  ↓
CAPTCHA: Invisible hCaptcha bypass
  ↓
CONSULTA: 2.1s average response
  ↓
VALIDAÇÃO: URL change detection
  ↓
RESULTADO: Certidão de débitos (9.6s total)
```

---

## ⚙️ CONFIGURAÇÕES TÉCNICAS

### Selenium WebDriver
```python
chrome_options = webdriver.ChromeOptions()
chrome_options.add_argument(f"--user-data-dir={profile_path}")
chrome_options.add_argument("--disable-blink-features=AutomationControlled")
chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
chrome_options.add_experimental_option('useAutomationExtension', False)
```

### Performance Settings
- **Timeouts**: Configuráveis por operação
- **Retry Logic**: Múltiplas tentativas
- **Memory Management**: Profile cleanup
- **Parallel Processing**: Onde aplicável

### Error Handling
```python
try:
    # Operação principal
    resultado = executar_operacao()
except WebDriverException as e:
    # Fallback strategy
    resultado = estrategia_alternativa()
except Exception as e:
    # Logging e recovery
    log_error(e)
    return None
```

---

## 📊 MÉTRICAS E MONITORAMENTO

### Logs Detalhados
- **Localização**: `logs_completos/`
- **Formato**: Timestamp + Level + Message
- **Rotação**: Por execução
- **Conteúdo**: Stack traces completos

### Performance Metrics
- **Receita Federal**: 9.6s total (objetivo ≤30s)
- **Download PDFs**: Variável por documento
- **Processamento IA**: Dependente do volume
- **Verificação**: Otimizada para grandes volumes

### Success Rates
- **Autenticação**: Alta (com fallback manual)
- **Download**: Múltiplas estratégias garantem sucesso
- **IA Processing**: Dependente da qualidade do PDF
- **Receita Federal**: 100% validado

---

## 🔧 MANUTENÇÃO E TROUBLESHOOTING

### Problemas Comuns Identificados
1. **Chrome Profile em uso**: Detectado nos logs
2. **WebSigner não detectado**: Fallback manual
3. **PDFs não baixados**: Estratégias alternativas
4. **Timeouts**: Configurações ajustáveis

### Soluções Implementadas
- **Profile management**: Cleanup automático
- **Retry mechanisms**: Múltiplas tentativas
- **User guidance**: Instruções claras
- **Logging detalhado**: Diagnóstico preciso

### Configuração Multi-usuário
- **Leoza**: `executar_TJSP_TXT_filtrado.bat`
- **Bipre**: `executar_TJSP_TXT_filtrado_BIPRE.bat`
- **Paths específicos**: Adaptação por usuário
- **ChromeDriver detection**: Múltiplas localizações

---

**Status**: ANÁLISE TÉCNICA COMPLETA ✅  
**Integração**: Sistema robusto e bem arquitetado  
**Recomendação**: Pronto para produção com monitoramento
