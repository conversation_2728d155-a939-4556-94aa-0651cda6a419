@echo off
title Verificador de Filtro TJSP
echo.
echo ========================================
echo   VERIFICADOR DE FILTRO TJSP
echo ========================================
echo.

REM Verificar se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado!
    echo Por favor, instale Python 3.7 ou superior
    pause
    exit /b 1
)

REM Verificar se o arquivo PDF existe
if not exist "pdf.pdf" (
    echo.
    echo AVISO: Arquivo 'pdf.pdf' nao encontrado!
    echo Por favor, coloque o arquivo PDF na pasta com o nome 'pdf.pdf'
    echo.
    pause
    exit /b 1
)

REM Instalar dependências se necessário
echo Verificando dependencias...
pip install -r requirements.txt --quiet

echo.
echo Iniciando verificacao do filtro TJSP...
echo.

REM Executar o script principal
python verificador_filtro_tjsp.py

echo.
echo Verificacao concluida!
pause
