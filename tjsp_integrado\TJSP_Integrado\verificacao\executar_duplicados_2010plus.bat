@echo off
title Verificador de Duplicados TJSP - Filtro 2010+
echo.
echo ========================================
echo   VERIFICADOR DE DUPLICADOS TJSP
echo   FILTRO 2010+ (Pagina 18800+)
echo ========================================
echo.

REM Verificar se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado!
    echo Por favor, instale Python 3.7 ou superior
    pause
    exit /b 1
)

REM Verificar se o arquivo PDF existe
if not exist "pdf.pdf" (
    echo.
    echo AVISO: Arquivo 'pdf.pdf' nao encontrado!
    echo Por favor, coloque o arquivo PDF na pasta com o nome 'pdf.pdf'
    echo.
    pause
    exit /b 1
)

REM Instalar dependências se necessário
echo Verificando dependencias...
pip install -r requirements.txt --quiet

echo.
echo Iniciando verificacao de duplicados (Filtro 2010+)...
echo Pagina inicial padrao: 18800
echo.

REM Executar o script de duplicados
python verificador_duplicados_2010plus.py

echo.
echo Verificacao de duplicados concluida!
echo.
echo Arquivos gerados:
echo - Relatorio Excel completo
echo - Lista TXT de numeros limpos (pronta para uso)
echo.
pause
