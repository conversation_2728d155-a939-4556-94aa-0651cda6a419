// Formato de armazenamento JSONL
private async loadGraph(): Promise<KnowledgeGraph> {
  const data = await fs.readFile(MEMORY_FILE_PATH, "utf-8");
  const lines = data.split("\n").filter(line => line.trim() !== "");
  return lines.reduce((graph: KnowledgeGraph, line) => {
    const item = JSON.parse(line); // Aqui ocorria o erro
    if (item.type === "entity") graph.entities.push(item as Entity);
    if (item.type === "relation") graph.relations.push(item as Relation);
    return graph;
  }, { entities: [], relations: [] });
}