#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Processador Completo TJSP - Versão TXT (Filtrado)
Processa números de autos de arquivo TXT já filtrado, valida no TJSP, e baixa ofícios requisitórios.
"""

import os
import sys
import time
import logging
import re
import pandas as pd
from datetime import datetime

try:
    from tqdm import tqdm
except ImportError:
    print("Biblioteca 'tqdm' não encontrada. Necessária para a barra de progresso.")
    print("Para instalar, execute: pip install tqdm")
    sys.exit(1)

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementNotInteractableException, InvalidSelectorException

# --- Configurações Globais ---
PERFIL_CHROME_PATH = "C:/Users/<USER>/ClineAutomationProfile_TJSP" 
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
DOWNLOAD_DIR = os.path.join(SCRIPT_DIR, "downloads_completos")
LOG_DIR = os.path.join(SCRIPT_DIR, "logs_completos")
URL_TJSP_CONSULTA = 'https://esaj.tjsp.jus.br/cpopg/open.do'
URL_TJSP_LOGIN = 'https://esaj.tjsp.jus.br/sajcas/login?service=https%3A%2F%2Fesaj.tjsp.jus.br%2Fesaj%2Fj_spring_cas_security_check'

# Nome do arquivo TXT com números filtrados
ARQUIVO_TXT_FILTRADO = "autosfiltrados.txt"

if SCRIPT_DIR not in sys.path:
    sys.path.append(SCRIPT_DIR)
logger = None 

def configurar_logs():
    global logger
    os.makedirs(LOG_DIR, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"processador_completo_tjsp_txt_{timestamp}.log"
    log_path = os.path.join(LOG_DIR, log_filename)
    if logging.getLogger().hasHandlers(): logging.getLogger().handlers.clear()
    file_handler = logging.FileHandler(log_path, encoding='utf-8')
    file_handler.setLevel(logging.INFO) 
    file_formatter = logging.Formatter('%(asctime)s [%(levelname)s] - %(funcName)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    file_handler.setFormatter(file_formatter)
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(logging.WARNING) 
    stream_formatter = logging.Formatter('[LOG-%(levelname)s] %(message)s')
    stream_handler.setFormatter(stream_formatter)
    logger_obj = logging.getLogger()
    logger_obj.setLevel(logging.INFO) 
    logger_obj.addHandler(file_handler); logger_obj.addHandler(stream_handler)
    logger = logger_obj 
    from selenium.webdriver.remote.remote_connection import LOGGER as selenium_logger
    selenium_logger.setLevel(logging.WARNING)
    return logger

def carregar_numeros_filtrados(caminho_arquivo_txt):
    """
    Carrega números de autos do arquivo TXT filtrado
    Retorna lista de números válidos
    """
    print(f"📋 Carregando números filtrados de: {os.path.basename(caminho_arquivo_txt)}")
    logger.info(f"Carregando números de: {caminho_arquivo_txt}")
    
    if not os.path.exists(caminho_arquivo_txt):
        print(f"❌ Arquivo TXT não encontrado: {caminho_arquivo_txt}")
        logger.error(f"Arquivo TXT não encontrado: {caminho_arquivo_txt}")
        return []
    
    numeros_validos = []
    numeros_invalidos = []
    
    try:
        with open(caminho_arquivo_txt, 'r', encoding='utf-8') as f:
            for linha_num, linha in enumerate(f, 1):
                numero = linha.strip()
                if not numero:  # Pular linhas vazias
                    continue
                
                # Validar formato do número
                if re.match(r'\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4}', numero):
                    numeros_validos.append(numero)
                else:
                    numeros_invalidos.append((linha_num, numero))
                    logger.warning(f"Linha {linha_num}: formato inválido '{numero}'")
        
        print(f"✅ Carregados {len(numeros_validos)} números válidos")
        if numeros_invalidos:
            print(f"⚠️ {len(numeros_invalidos)} números com formato inválido ignorados")
            logger.warning(f"{len(numeros_invalidos)} números inválidos ignorados")
        
        logger.info(f"Total números válidos carregados: {len(numeros_validos)}")
        return numeros_validos
        
    except Exception as e:
        print(f"❌ Erro ao ler arquivo TXT: {e}")
        logger.error(f"Erro ao ler arquivo TXT: {e}", exc_info=True)
        return []

def configurar_chrome_unificado():
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized"); chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    logger.info(f"Usando diretório de dados do usuário: {PERFIL_CHROME_PATH}")
    chrome_options.add_argument(f'--user-data-dir={PERFIL_CHROME_PATH}')
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option("useAutomationExtension", False)
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    prefs = {
        "download.prompt_for_download": False, "download.default_directory": DOWNLOAD_DIR,
        "download.directory_upgrade": True, "safebrowsing.enabled": True,
        "plugins.always_open_pdf_externally": True 
    }
    chrome_options.add_experimental_option("prefs", prefs)
    return chrome_options

def iniciar_navegador_unificado():
    try:
        chrome_options = configurar_chrome_unificado()
        print("🔵 Iniciando navegador Chrome...")
        driver = webdriver.Chrome(options=chrome_options)
        logger.info("Navegador Chrome iniciado"); print("✅ Navegador Chrome iniciado.")
        return driver
    except Exception as e:
        print(f"❌ Erro ao iniciar o navegador: {e}"); logger.error(f"Erro ao iniciar o navegador: {e}", exc_info=True); raise

def autenticar_usuario_unificado(driver):
    try:
        print("🔵 Realizando autenticação no TJSP..."); logger.info("Iniciando autenticação manual.")
        driver.get(URL_TJSP_LOGIN); time.sleep(2) 
        print("\n" + "="*80 + "\n🔑 AUTENTICAÇÃO NECESSÁRIA 🔑".center(80) + "\n" + "="*80 +
              "\n\nSiga estas etapas para autenticação" +
              "\n\nIMPORTANTE: Nome deve aparecer no canto superior direito do portal." +
              "\n\n>> Pressione Enter SOMENTE APÓS concluir a autenticação completa... ")
        input(); time.sleep(3) 
        nome_usuario_detectado = "denis henrique sousa oliveira" 
        if nome_usuario_detectado in driver.page_source.lower(): 
            logger.info(f"Autenticação OK (nome encontrado).")
            print(f"✅ Autenticação confirmada (nome encontrado)!")
            return True
        else:
            logger.warning("Nome não confirmado automaticamente."); print("⚠️ Nome não confirmado automaticamente.")
            confirmacao = input("Confirma que a autenticação está completa e seu nome aparece no portal? (s/n): ").strip().lower()
            if confirmacao == 's': logger.info("Autenticação confirmada pelo usuário."); print("✅ Autenticação confirmada!"); return True
            else: logger.error("Autenticação não confirmada."); print("❌ Autenticação não confirmada."); return False
    except Exception as e:
        print(f"❌ Erro na autenticação: {e}"); logger.error(f"Erro na autenticação: {e}", exc_info=True); return False

def consultar_processo_principal(driver, numero_autos_origem_txt, numero_processo_formatado_tjsp, comarca_formatada_tjsp):
    try:
        print(f" Consultando processo: {numero_autos_origem_txt}...")
        logger.info(f"Consultando {numero_autos_origem_txt} (TJSP: {numero_processo_formatado_tjsp} / Foro: {comarca_formatada_tjsp})")
        driver.get(URL_TJSP_CONSULTA)
        WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.ID, 'numeroDigitoAnoUnificado')))
        driver.find_element(By.ID, 'numeroDigitoAnoUnificado').clear(); time.sleep(0.1)
        driver.find_element(By.ID, 'numeroDigitoAnoUnificado').send_keys(numero_processo_formatado_tjsp)
        driver.find_element(By.ID, 'foroNumeroUnificado').clear(); time.sleep(0.1)
        driver.find_element(By.ID, 'foroNumeroUnificado').send_keys(comarca_formatada_tjsp)
        driver.find_element(By.ID, 'botaoConsultarProcessos').click(); time.sleep(3) 

        pg_src_lower = driver.page_source.lower(); url_lower = driver.current_url.lower()
        if ("dados do processo" in pg_src_lower or "classe:" in pg_src_lower or "cpopg/show.do" in url_lower):
            print(f" Detalhes de {numero_autos_origem_txt} carregados diretamente."); logger.info(f"Detalhes de {numero_autos_origem_txt} carregados diretamente."); return True
        if "processos encontrados" in pg_src_lower:
            print(f"ℹ Lista de resultados para {numero_autos_origem_txt}."); logger.info(f"Lista de resultados para {numero_autos_origem_txt}.")
            try:
                link = WebDriverWait(driver, 8).until(EC.element_to_be_clickable((By.LINK_TEXT, numero_autos_origem_txt))) 
                print(f"🔗 Link para {numero_autos_origem_txt} encontrado. Clicando..."); logger.info(f"Link para {numero_autos_origem_txt} encontrado. Clicando...")
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", link); time.sleep(0.3)
                link.click(); time.sleep(3) 
                pg_src_apos_clique = driver.page_source.lower(); url_apos_clique = driver.current_url.lower()
                if ("dados do processo" in pg_src_apos_clique or "classe:" in pg_src_apos_clique or "cpopg/show.do" in url_apos_clique):
                    print(f" Detalhes de {numero_autos_origem_txt} carregados após clique."); logger.info(f"Detalhes de {numero_autos_origem_txt} carregados após clique."); return True
                else:
                    print(f"⚠️ Detalhes de {numero_autos_origem_txt} não confirmados após clique."); logger.warning(f"Detalhes de {numero_autos_origem_txt} não confirmados após clique. URL: {driver.current_url}"); return False
            except TimeoutException: print(f"❌ Link para {numero_autos_origem_txt} não encontrado na lista."); logger.error(f"Link para {numero_autos_origem_txt} não encontrado na lista."); return False
            except Exception as e: print(f"❌ Erro ao clicar no link da lista para {numero_autos_origem_txt}: {e}"); logger.error(f"Erro ao clicar no link da lista para {numero_autos_origem_txt}: {e}", exc_info=True); return False
        if "não existem informações disponíveis" in pg_src_lower:
            print(f"❌ Processo {numero_autos_origem_txt} não encontrado."); logger.error(f"Processo {numero_autos_origem_txt} não encontrado."); return False
        print(f"⚠️ Consulta de {numero_autos_origem_txt} em página inesperada."); logger.warning(f"Página inesperada para {numero_autos_origem_txt}. URL: {driver.current_url}"); return False 
    except Exception as e:
        print(f"❌ Erro geral ao consultar {numero_autos_origem_txt}: {e}"); logger.error(f"Erro geral ao consultar {numero_autos_origem_txt}: {e}", exc_info=True); return False

def encontrar_oficio_requisitorio(driver):
    print("📄 Buscando 'Ofício Requisitório-Precatório Expedido'...")
    logger.info("Procurando 'Ofício Requisitório-Precatório Expedido'...")
    try:
        time.sleep(1.1); 
        xpath_exato = "//a[normalize-space()='Ofício Requisitório-Precatório Expedido']"
        try:
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, xpath_exato)))
            try:
                 el_temp = driver.find_element(By.XPATH, xpath_exato)
                 driver.execute_script("arguments[0].scrollIntoView({block: 'center', inline: 'nearest'});", el_temp)
                 time.sleep(0.3)
            except: pass
            link_oficio = WebDriverWait(driver, 6).until(EC.element_to_be_clickable((By.XPATH, xpath_exato)))
            logger.info(f"Link '{link_oficio.text}' encontrado!")
            link_oficio.click(); time.sleep(1.6) 
            if len(driver.window_handles) > 1:
                driver.switch_to.window(driver.window_handles[-1])
                print("🔄 Mudou para nova janela com documento."); logger.info(f"Mudou para nova janela/aba. URL: {driver.current_url}")
                return True
            else: print("⚠️ Link clicado, mas nova janela não abriu."); logger.warning("Link clicado, mas nova janela não abriu."); return False 
        except TimeoutException:
            print("❌ Link 'Ofício Requisitório-Precatório Expedido' não encontrado ou não clicável."); logger.error("Link exato não encontrado/clicável."); return False
    except Exception as e:
        print(f"❌ Erro ao buscar Ofício: {e}"); logger.error(f"Erro ao buscar Ofício: {e}", exc_info=True); return False

def limpar_janelas_extras_e_focar_principal(driver, main_window_handle):
    try:
        if not driver or not main_window_handle: return
        handles = driver.window_handles
        if len(handles) > 1:
            logger.debug(f"Limpando janelas. Principal: {main_window_handle}. Abertas: {handles}")
            for h in handles:
                if h != main_window_handle:
                    try: driver.switch_to.window(h); driver.close(); logger.debug(f"Fechada: {h}")
                    except: pass 
            driver.switch_to.window(main_window_handle)
        elif driver.current_window_handle != main_window_handle:
            try: driver.switch_to.window(main_window_handle)
            except: logger.error("Falha ao focar na janela principal após limpeza.")
        time.sleep(0.2) 
    except Exception as e: logger.error(f"Erro ao limpar janelas: {e}")

def identificar_novo_arquivo_download(diretorio, arquivos_antes, timeout=4):
    tempo_inicial = time.time()
    while time.time() - tempo_inicial < timeout:
        arquivos_atuais = set(os.listdir(diretorio))
        novos_arquivos_pdf = [f for f in (arquivos_atuais - arquivos_antes) if f.lower().endswith('.pdf')]
        if novos_arquivos_pdf:
            caminhos_completos = [os.path.join(diretorio, nome) for nome in novos_arquivos_pdf]
            arquivo_mais_recente = max(caminhos_completos, key=os.path.getmtime, default=None)
            if arquivo_mais_recente:
                nome_arquivo_real = os.path.basename(arquivo_mais_recente)
                logger.info(f"Novo arquivo de download identificado: {nome_arquivo_real}")
                return nome_arquivo_real
        time.sleep(0.5)
    logger.warning("Nenhum novo arquivo PDF de download identificado dentro do timeout.")
    return None

try:
    from tjsp_download import atualizar_baixar_documento as tjsp_atualizar_baixar_documento
    if logger: logger.info("tjsp_download.py importado.")
    else: configurar_logs(); logger.info("tjsp_download.py importado.") 
    TJSP_DOWNLOAD_AVAILABLE = True
except ImportError:
    if logger: logger.error("Falha ao importar tjsp_download.py. Usando fallback.")
    else: configurar_logs(); logger.error("Falha ao importar tjsp_download.py. Usando fallback.")
    TJSP_DOWNLOAD_AVAILABLE = False

def download_documento_unificado(driver, nome_arquivo_base_sugerido, diretorio_destino):
    nome_arquivo_sugerido_com_ext = f"{nome_arquivo_base_sugerido}.pdf"
    print(f"📥 Fazendo download do Oficio")
    arquivos_antes_download = set()
    try:
        arquivos_antes_download = set(os.listdir(diretorio_destino))
    except FileNotFoundError:
        logger.warning(f"Diretório de download {diretorio_destino} não encontrado antes do download. Será criado.")
        os.makedirs(diretorio_destino, exist_ok=True)

    if TJSP_DOWNLOAD_AVAILABLE:
        sucesso_inicio_download = tjsp_atualizar_baixar_documento(driver, nome_arquivo_sugerido_com_ext, diretorio_destino)
    else: 
        sucesso_inicio_download = tjsp_atualizar_baixar_documento(driver, nome_arquivo_sugerido_com_ext, diretorio_destino)

    if sucesso_inicio_download:
        nome_real_baixado = identificar_novo_arquivo_download(diretorio_destino, arquivos_antes_download)
        if nome_real_baixado:
            logger.info(f"Download bem-sucedido. Arquivo real: {nome_real_baixado}. Nome sugerido: {nome_arquivo_sugerido_com_ext}")
            return nome_real_baixado 
        else:
            print(f"⚠️ Download iniciado, mas não foi possível identificar o novo arquivo na pasta {diretorio_destino}.")
            logger.warning(f"Download para {nome_arquivo_sugerido_com_ext} iniciado, mas arquivo real não identificado.")
            return "ARQUIVO_NAO_IDENTIFICADO" 
    else:
        print(f"❌ Falha ao iniciar o processo de download para {nome_arquivo_sugerido_com_ext}.")
        logger.error(f"Falha ao iniciar o processo de download para {nome_arquivo_sugerido_com_ext}.")
        return None

def extrair_ano_processo(numero_autos):
    match = re.search(r'\d{7}-\d{2}\.(\d{4})\.\d\.\d{2}\.\d{4}', numero_autos)
    return int(match.group(1)) if match else None

def preparar_numero_para_tjsp(numero_autos):
    match = re.match(r'(\d{7}-\d{2})\.(\d{4})\.\d\.\d{2}\.(\d{4})', numero_autos)
    if not match: logger.error(f"Formato inesperado: {numero_autos}"); return None, None
    return match.group(1).replace('-', '') + match.group(2), match.group(3)

def clicar_botao_mais_movimentacoes(driver): 
    try:
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight*0.7);"); time.sleep(0.5)
        botoes_mais = driver.find_elements(By.ID, "linkmovimentacoes")
        for bm in reversed(botoes_mais): 
            if bm.is_displayed() and bm.is_enabled():
                logger.info("Clicando 'Mais' das movimentações."); print("- Clicando no botão 'Mais'...")
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", bm); time.sleep(0.3)
                try: bm.click()
                except: driver.execute_script("arguments[0].click();", bm)
                time.sleep(1.5); return True 
        print("ℹ️ Botão 'Mais' não encontrado/habilitado."); logger.info("Botão 'Mais' não encontrado/habilitado.")
    except Exception as e: print(f"⚠️ Erro ao clicar 'Mais': {e}"); logger.info(f"Erro ao clicar 'Mais': {e}")
    return False

def verificar_palavras_proibidas_mov(driver, clicar_mais=True): 
    palavras_proibidas = ["mandado de levantamento eletrônico", "mandado de levantamento", "cessão de crédito"]
    print("🔍 Verificando palavras proibidas nas movimentações...")
    try:
        if clicar_mais and not clicar_botao_mais_movimentacoes(driver):
            logger.warning("Não clicou 'Mais', verificação de palavras pode ser incompleta.")
        texto_mov = ""; fonte = "Nenhuma"
        try:
            el_tabela = driver.find_element(By.ID, "tabelaTodasMovimentacoes")
            texto_mov = el_tabela.text.lower(); fonte = "ID tabela"
        except:
            try:
                movs = driver.find_elements(By.XPATH, "//table[contains(@class, 'movimentacoes')]//tr/td[2]")
                if movs: texto_mov = " ".join([m.text.lower() for m in movs]); fonte = "XPath tabela"
                else: texto_mov = driver.find_element(By.TAG_NAME, "body").text.lower(); fonte = "body"
            except: texto_mov = driver.page_source.lower(); fonte = "page_source"
        logger.debug(f"Texto para palavras proibidas de: {fonte}")
        for p in palavras_proibidas:
            if p in texto_mov: print(f"🚫 Palavra proibida: {p}"); logger.info(f"Palavra proibida: {p}"); return True
        print("✅ Nenhuma palavra proibida."); logger.info("Nenhuma palavra proibida."); return False
    except Exception as e: print(f"⚠️ Erro ao verificar palavras: {e}"); logger.error(f"Erro ao verificar palavras: {e}", exc_info=True); return False 

def verificar_status_processo(driver): 
    print("🔍 Verificando status do processo...")
    try:
        status_elements = driver.find_elements(By.XPATH, 
            "//span[contains(@class, 'tag') and (contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'arquivado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'extinto') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'cancelado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'baixado'))] | " +
            "//div[contains(@class, 'status-processo') and (contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'arquivado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'extinto') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'cancelado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'baixado'))] | " + 
            "//div[contains(text(),'Situação do Processo:')]/following-sibling::div[1] |" +  
            "//span[contains(@style, 'color: red') and (contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'arquivado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'extinto') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'cancelado') or contains(translate(., 'ARQUIVDOEXTNCLB', 'arquivdoextnclb'), 'baixado'))] |" + 
            "//span[contains(text(),'Extinto') or contains(text(),'Arquivado') or contains(text(),'Cancelado') or contains(text(),'Baixado')]" 
        )
        for el in status_elements:
            if el.is_displayed():
                texto_el = el.text.strip().lower()
                if any(s in texto_el for s in ["extinto", "arquivado", "baixado", "cancelado"]):
                    status_print = "Extinto" if "extinto" in texto_el else \
                                   "Arquivado" if "arquivado" in texto_el else \
                                   "Baixado" if "baixado" in texto_el else \
                                   "Cancelado" if "cancelado" in texto_el else \
                                   texto_el.capitalize()
                    print(f"🚫 Status: {status_print}"); logger.info(f"Status 'finalizado' detectado: {texto_el}")
                    return "finalizado"
        pg_lower = driver.page_source.lower()
        if any(s in pg_lower for s in ["processo extinto", "processo arquivado", "processo baixado", "processo cancelado"]):
            print("🚫 Status Finalizado (texto da página)."); logger.info("Status 'finalizado' (texto da página).")
            return "finalizado"
        print("✅ Status: Normal/Em Andamento."); logger.info("Status: normal/em andamento.")
        return "normal"
    except Exception as e: print(f"⚠️ Erro ao verificar status: {e}. Assumindo 'Normal'."); logger.warning(f"Erro ao verificar status: {e}. Assumindo 'normal'."); return "normal"

def _extrair_nome_reqte_da_pagina(driver):
    reqte_nome = ""
    try:
        label_td = driver.find_element(By.XPATH, "//tr[contains(@class, 'fundoClaro') or contains(@class, 'fundoEscuro')]/td[.//span[normalize-space(text())='Reqte:'] or normalize-space(text())='Reqte:']")
        nome_td = label_td.find_element(By.XPATH, "./following-sibling::td[1]")
        reqte_nome = nome_td.text.strip().split('\n')[0].strip() 
        if reqte_nome: logger.debug(f"Reqte (S1): {reqte_nome}"); return reqte_nome
    except NoSuchElementException: logger.debug("Reqte (S1) não encontrado.")
    try:
        reqte_element = driver.find_element(By.XPATH, "//span[contains(text(),'Reqte')]/ancestor::tr/td[@class='nomeParteEAdvogado']")
        reqte_nome = reqte_element.text.strip().split('\n')[0].strip()
        if reqte_nome: logger.debug(f"Reqte (S2): {reqte_nome}"); return reqte_nome
    except NoSuchElementException: logger.debug("Reqte (S2) não encontrado.")
    try:
        reqte_div = driver.find_element(By.XPATH, "//div[contains(@class,'label') and contains(text(),'Reqte:')]/following-sibling::div[contains(@class,'valor')][1]")
        reqte_nome = reqte_div.text.strip().split('\n')[0].strip()
        if reqte_nome: logger.debug(f"Reqte (S3): {reqte_nome}"); return reqte_nome
    except NoSuchElementException: logger.debug("Reqte (S3) não encontrado.")
    try:
        elementos = driver.find_elements(By.XPATH, "//*[contains(text(), 'Reqte:')]")
        for el in elementos:
            txt_completo = el.find_element(By.XPATH, "./parent::*").text
            match = re.search(r"Reqte:\s*(.+)", txt_completo, re.I)
            if match:
                cand = match.group(1).split('\n')[0].strip()
                if cand and len(cand) > 2 and not cand.lower().startswith("advogad"): 
                    reqte_nome = cand; logger.debug(f"Reqte (S4): {reqte_nome}"); return reqte_nome
    except Exception as e: logger.debug(f"Erro Reqte (S4): {e}")
    return reqte_nome

def verificar_partes_processo(driver): 
    palavras_proibidas = ["ltda", "s/a", "s.a", "eireli", "fundo", "prefeitura", "previdência", "fazenda", "associação", "cooperativa", "consórcio", "companhia", "instituto", "estado de", "sociedade", "conselho", "universidade", "investimento", "município de", "banco", "secretaria", "departamento", "cia", "epp", "me", "usina", "itau"]
    print("🔍 Verificando partes do processo (Reqte)..."); logger.info("Verificando partes (Reqte)...")
    reqte_nome_original = _extrair_nome_reqte_da_pagina(driver)
    if not reqte_nome_original: print("⚠️ Reqte não localizado."); logger.error("Reqte não localizado para verificação de partes."); return False 
    reqte_nome_lower = reqte_nome_original.lower() 
    print(f"ℹ- Reqte: {reqte_nome_original}"); logger.info(f"Reqte para verificação de partes: {reqte_nome_original} (lower: {reqte_nome_lower})")
    for p_lower in palavras_proibidas: 
        if re.search(r'\b' + re.escape(p_lower) + r'\b', reqte_nome_lower):
            print(f"🚫 Parte proibida (PJ) encontrada no Reqte: '{p_lower}' (em '{reqte_nome_original}')"); 
            logger.info(f"Parte proibida (PJ) encontrada no Reqte: '{p_lower}' em '{reqte_nome_original}'"); 
            return True 
    print("✅ Reqte válido."); return False 

def obter_nome_cliente_do_precatorio(driver):
    print("👤 Buscando nome do cliente (Reqte) no precatório..."); logger.info("Buscando nome do cliente (Reqte)...")
    reqte_nome_extraido = _extrair_nome_reqte_da_pagina(driver)
    if not reqte_nome_extraido:
        print("⚠️ Nome do cliente não localizado."); logger.error("Nome do cliente não localizado.")
        return "CLIENTE NÃO LOCALIZADO"
    reqte_nome_limpo = re.sub(r'\s+e\s+outro(s)?\s*$', '', reqte_nome_extraido, flags=re.I).strip()
    print(f"- Nome do cliente: {reqte_nome_limpo}"); logger.info(f"Nome do cliente: {reqte_nome_limpo}"); return reqte_nome_limpo

def extrair_numero_precatorio_completo_da_pagina(driver, numero_autos_principal):
    print("🆔 Extraindo número completo do precatório..."); 
    logger.info(f"Extraindo N° precatório para principal: {numero_autos_principal}")
    try:
        xpath_num_prec = f"//span[@class='unj-larger' and contains(normalize-space(.), '{numero_autos_principal}') and contains(normalize-space(.), '(') and contains(normalize-space(.), ')')]"
        
        elementos_num_prec = []
        try:
            elementos_num_prec = driver.find_elements(By.XPATH, xpath_num_prec)
        except InvalidSelectorException: 
             logger.error(f"XPath principal para número do precatório é inválido: {xpath_num_prec}. Usando fallback.")
             return "USAR_FALLBACK_TEXTO_LINK"
        except Exception as e_find: 
            logger.error(f"Erro ao buscar elementos com XPath {xpath_num_prec}: {e_find}")
            return "USAR_FALLBACK_TEXTO_LINK"

        for el_num_prec in elementos_num_prec:
            if el_num_prec.is_displayed():
                texto_completo = el_num_prec.text.strip()
                logger.debug(f"Texto candidato (span.unj-larger): '{texto_completo}'")
                match = re.search(r"(?P<num_base>\d{7}-\d{2}\.\d{4}\.\d\.\d{2}\.\d{4})\s*\((?P<sufixo>\d+)\)", texto_completo)
                if match:
                    num_base_extraido = match.group("num_base"); suf_ext_str = match.group("sufixo")
                    if num_base_extraido == numero_autos_principal:
                        try: suf_final = str(int(suf_ext_str)).zfill(2)
                        except ValueError: suf_final = suf_ext_str.zfill(2) 
                        num_formatado = f"{num_base_extraido} ({suf_final})"
                        print(f" Número do precatório (HTML span.unj-larger): {num_formatado}"); 
                        logger.info(f"Número do precatório extraído de span.unj-larger: {num_formatado}")
                        return num_formatado
                    else:
                        logger.warning(f"Num base extraído '{num_base_extraido}' de span.unj-larger difere do principal '{numero_autos_principal}'.")
        
        logger.info("N° precatório não encontrado via span.unj-larger ou N° base divergente. Usar fallback do texto do link.");
        return "USAR_FALLBACK_TEXTO_LINK" 
    except Exception as e: 
        print(f"❌ Erro ao extrair N° precatório da página: {e}"); 
        logger.error(f"Erro ao extrair N° precatório da página: {e}", exc_info=True); 
        return "ERRO_EXTRACAO_DA_PAGINA"

# --- Função Principal de Orquestração (Modificada para TXT) ---
def processador_completo_tjsp_txt(txt_caminho_entrada, excel_saida_nome, numero_inicial=1):
    global logger
    logger = configurar_logs() 
    print(f"🚀 Iniciando processamento da lista TXT: {os.path.basename(txt_caminho_entrada)}")
    logger.info(f"Iniciando processamento: TXT: {txt_caminho_entrada}, Excel: {excel_saida_nome}, Downloads: {DOWNLOAD_DIR}")

    lista_resultados_finais = [] 
    driver = None

    try:
        # Carregar números filtrados do TXT
        numeros_filtrados = carregar_numeros_filtrados(txt_caminho_entrada)
        if not numeros_filtrados:
            print("❌ Nenhum número válido carregado. Encerrando.")
            return

        total_numeros = len(numeros_filtrados)
        print(f"📊 Total de números a processar: {total_numeros}")
        
        # Validar número inicial
        if not (1 <= numero_inicial <= total_numeros):
            print(f"⚠️ Número inicial ({numero_inicial}) inválido (1-{total_numeros}). Usando 1.")
            logger.warning(f"Número inicial ({numero_inicial}) inválido. Usando 1."); numero_inicial = 1

        driver = iniciar_navegador_unificado()
        if not autenticar_usuario_unificado(driver): logger.critical("Autenticação falhou. Encerrando."); return
        main_window_handle = driver.current_window_handle

        print(f"🔄 Processando a partir do número {numero_inicial} de {total_numeros}")
        logger.info(f"Processando números {numero_inicial} até {total_numeros}")

        # Processar cada número da lista
        for idx_numero in tqdm(range(numero_inicial - 1, total_numeros), desc="Processando Números", unit="num"):
            numero_display = idx_numero + 1
            numero_autos_origem_txt = numeros_filtrados[idx_numero]
            
            print(f"\n\n{'-'*30} NÚMERO {numero_display}/{total_numeros} {'-'*30}")
            print(f"🔢 Processando: {numero_autos_origem_txt}")
            start_time_numero = time.time()
            
            logger.info(f"Processando número {numero_display}/{total_numeros}: {numero_autos_origem_txt}")
            
            # NOTA: Filtros de ano e terminação 0500 REMOVIDOS - já aplicados no TXT
            
            num_proc_tjsp, comarca_tjsp = preparar_numero_para_tjsp(numero_autos_origem_txt)
            if not num_proc_tjsp: 
                print(f"❌ {numero_autos_origem_txt}: Falha formatar N°."); 
                logger.error(f"{numero_autos_origem_txt}: Falha formatar N°."); 
                continue 

            if not consultar_processo_principal(driver, numero_autos_origem_txt, num_proc_tjsp, comarca_tjsp):
                limpar_janelas_extras_e_focar_principal(driver, main_window_handle); time.sleep(0.5); continue 
            
            status_pp = verificar_status_processo(driver)
            if status_pp == "finalizado":
                print(f"🚫 Proc principal {numero_autos_origem_txt} status 'Finalizado'. Pulando."); 
                logger.info(f"Proc principal {numero_autos_origem_txt} status 'Finalizado'."); 
                limpar_janelas_extras_e_focar_principal(driver, main_window_handle); time.sleep(0.5); continue
            
            partes_proibidas_pp = verificar_partes_processo(driver)
            if partes_proibidas_pp:
                print(f"🚫 Proc principal {numero_autos_origem_txt} Parte Proibida. Pulando."); 
                logger.info(f"Proc principal {numero_autos_origem_txt} Parte Proibida."); 
                limpar_janelas_extras_e_focar_principal(driver, main_window_handle); time.sleep(0.5); continue

            palavras_proibidas_pp = verificar_palavras_proibidas_mov(driver, clicar_mais=True)
            if palavras_proibidas_pp:
                print(f"🚫 Proc principal {numero_autos_origem_txt} Palavra Proibida Mov. Pulando."); 
                logger.info(f"Proc principal {numero_autos_origem_txt} Palavra Proibida Mov."); 
                limpar_janelas_extras_e_focar_principal(driver, main_window_handle); time.sleep(0.5); continue
            
            print(f"✅ Proc principal {numero_autos_origem_txt} validado."); 
            logger.info(f"Proc principal {numero_autos_origem_txt} validado (TJSP).")
            url_processo_principal_com_precatorios = driver.current_url

            xpath_links_precatorios = "//a[@class='incidente' and starts-with(normalize-space(.), 'Precatório -')]"
            xpath_links_precatorios_fallback = "//a[starts-with(normalize-space(.), 'Precatório') and contains(@href,'cpopg/show.do?processo') and string-length(normalize-space(substring-after(normalize-space(.), 'Precatório')))>0]"
            links_prec_elems = []
            
            try:
                WebDriverWait(driver, 5).until(EC.presence_of_all_elements_located((By.XPATH, xpath_links_precatorios)))
                links_prec_elems = driver.find_elements(By.XPATH, xpath_links_precatorios)
                if links_prec_elems: logger.info(f"Links de precatório encontrados com seletor principal para {numero_autos_origem_txt}.")
            except TimeoutException:
                logger.info(f"Nenhum link de precatório (seletor principal) para {numero_autos_origem_txt}. Tentando fallback...")
                try:
                    WebDriverWait(driver, 3).until(EC.presence_of_all_elements_located((By.XPATH, xpath_links_precatorios_fallback)))
                    links_prec_elems = driver.find_elements(By.XPATH, xpath_links_precatorios_fallback)
                    if links_prec_elems: logger.info(f"Links de precatório encontrados com seletor fallback para {numero_autos_origem_txt}.")
                except TimeoutException: pass

            if not links_prec_elems:
                print(f"ℹ️ Nenhum link 'Precatório' clicável para {numero_autos_origem_txt}."); 
                logger.info(f"Nenhum link 'Precatório' clicável para {numero_autos_origem_txt}.")
                limpar_janelas_extras_e_focar_principal(driver, main_window_handle); time.sleep(0.5); continue
            
            num_total_links = len(links_prec_elems)
            print(f"🔗 {num_total_links} link(s) 'Precatório' para {numero_autos_origem_txt}."); 
            logger.info(f"{num_total_links} link(s) 'Precatório' para {numero_autos_origem_txt}.")
            
            for i_link in range(num_total_links):
                try:
                    driver.get(url_processo_principal_com_precatorios) 
                    current_xpath_to_use = xpath_links_precatorios 
                    try:
                        WebDriverWait(driver, 5).until(EC.presence_of_all_elements_located((By.XPATH, current_xpath_to_use)))
                    except TimeoutException: 
                        logger.debug(f"Seletor principal de precatório falhou, tentando fallback: {xpath_links_precatorios_fallback}")
                        current_xpath_to_use = xpath_links_precatorios_fallback
                        WebDriverWait(driver, 4).until(EC.presence_of_all_elements_located((By.XPATH, current_xpath_to_use)))
                    time.sleep(1.2) 
                    
                    links_atualizados = driver.find_elements(By.XPATH, current_xpath_to_use)
                    if i_link >= len(links_atualizados): logger.warning(f"Índice {i_link} fora dos limites. Pulando."); break
                    
                    link_prec_atual = links_atualizados[i_link]; texto_link = link_prec_atual.text.strip()
                    if not texto_link or not texto_link.lower().startswith("precatório"):
                        logger.warning(f"Link {i_link+1} com texto '{texto_link}' não parece ser um precatório válido. Pulando.")
                        continue

                    print(f"\n- Processando link Precatório {i_link+1}/{num_total_links}: '{texto_link}'")
                    logger.info(f"Processando link Precatório {i_link+1}/{num_total_links}: '{texto_link}'")
                    
                    WebDriverWait(driver, 5).until(EC.element_to_be_clickable(link_prec_atual))
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", link_prec_atual); time.sleep(0.4)
                    link_prec_atual.click()
                    WebDriverWait(driver, 8).until(EC.url_changes(url_processo_principal_com_precatorios)); time.sleep(1.4) 

                    num_prec_tjsp_extraido_pagina = extrair_numero_precatorio_completo_da_pagina(driver, numero_autos_origem_txt)
                    
                    if "USAR_FALLBACK_TEXTO_LINK" in num_prec_tjsp_extraido_pagina or \
                       "NÚMERO PRECATÓRIO NÃO EXTRAÍDO DA PÁGINA" in num_prec_tjsp_extraido_pagina or \
                       "ERRO_EXTRACAO_DA_PAGINA" in num_prec_tjsp_extraido_pagina:
                        
                        sufixo_do_link = re.sub(r'Precatório\s*(Nº|nº|N°|n°)?\s*-\s*', '', texto_link, flags=re.I).strip()
                        try:
                            sufixo_numerico = int(sufixo_do_link)
                            sufixo_formatado = str(sufixo_numerico).zfill(2)
                        except ValueError:
                            sufixo_formatado = sufixo_do_link 
                        
                        num_prec_tjsp = f"{numero_autos_origem_txt} ({sufixo_formatado})"
                        print(f"- N° precatório: {num_prec_tjsp}")
                        logger.info(f"N° precatório formado via texto do link (fallback principal): {num_prec_tjsp}")
                    else:
                        num_prec_tjsp = num_prec_tjsp_extraido_pagina 
                    
                    nome_cliente_prec = obter_nome_cliente_do_precatorio(driver)

                    status_prec = verificar_status_processo(driver)
                    if status_prec == "finalizado":
                        print(f"🚫 Precatório '{num_prec_tjsp}' status 'Finalizado'. Pulando."); 
                        logger.info(f"Precatório '{num_prec_tjsp}' status 'Finalizado'."); continue
                    
                    partes_prec = verificar_partes_processo(driver)
                    if partes_prec:
                        print(f"🚫 Precatório '{num_prec_tjsp}' Parte Proibida. Pulando."); 
                        logger.info(f"Precatório '{num_prec_tjsp}' Parte Proibida."); continue
                    
                    palavras_prec = verificar_palavras_proibidas_mov(driver, clicar_mais=True)
                    if palavras_prec:
                        print(f"🚫 Precatório '{num_prec_tjsp}' Palavra Proibida. Pulando."); 
                        logger.info(f"Precatório '{num_prec_tjsp}' Palavra Proibida."); continue
                    
                    print(f"✅ Precatório '{num_prec_tjsp}' validado."); 
                    logger.info(f"Precatório '{num_prec_tjsp}' validado. Tentando baixar ofício.")
                    
                    resultado_item_precatorio = {
                        "NumeroLista": numero_display, 
                        "NumeroAutosTXT": numero_autos_origem_txt,
                        "StatusProcessoPrincipal": "Válido (TJSP)", 
                        "NumeroPrecatorioCompletoTJSP": num_prec_tjsp, 
                        "NomeClientePrecatorio": nome_cliente_prec, 
                        "StatusValidacaoPrecatorio": "Válido", 
                        "StatusDownloadOficio": "Não Tentado", 
                        "NomeArquivoBaixado": "",
                        "DataHoraProcessamento": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

                    if encontrar_oficio_requisitorio(driver): 
                        nome_base_sugerido = f"Oficio_Requisitorio_{num_prec_tjsp.replace(' ','_').replace('(','').replace(')','').replace('/','-').replace('.','_').replace(':','-')}"
                        nome_real_arquivo_baixado = download_documento_unificado(driver, nome_base_sugerido, DOWNLOAD_DIR)
                        
                        if nome_real_arquivo_baixado and nome_real_arquivo_baixado != "ARQUIVO_NAO_IDENTIFICADO":
                            print(f"✅ Download p/ '{num_prec_tjsp}' CONCLUÍDO como '{nome_real_arquivo_baixado}'."); 
                            logger.info(f"Download p/ '{num_prec_tjsp}' CONCLUÍDO como '{nome_real_arquivo_baixado}'.")
                            resultado_item_precatorio["StatusDownloadOficio"] = "Sucesso"; 
                            resultado_item_precatorio["NomeArquivoBaixado"] = nome_real_arquivo_baixado
                        elif nome_real_arquivo_baixado == "ARQUIVO_NAO_IDENTIFICADO":
                            print(f"⚠️ Download p/ '{num_prec_tjsp}' talvez concluído, mas nome do arquivo não identificado."); 
                            logger.warning(f"Download p/ '{num_prec_tjsp}' talvez concluído, mas nome não identificado.")
                            resultado_item_precatorio["StatusDownloadOficio"] = "Sucesso (Nome Não Identificado)"; 
                            resultado_item_precatorio["NomeArquivoBaixado"] = "VERIFICAR PASTA MANUALMENTE"
                        else: 
                            print(f"❌ Falha download p/ '{num_prec_tjsp}'."); 
                            logger.info(f"Falha download p/ '{num_prec_tjsp}'.")
                            resultado_item_precatorio["StatusDownloadOficio"] = "Falha Download"
                    else:
                        resultado_item_precatorio["StatusDownloadOficio"] = "Ofício Não Encontrado"
                    
                    lista_resultados_finais.append(resultado_item_precatorio)
                
                except Exception as e:
                    print(f"❌ Erro ao processar link Precatório '{texto_link if 'texto_link' in locals() else 'desconhecido'}': {e}")
                    logger.error(f"Erro ao processar link Precatório '{texto_link if 'texto_link' in locals() else 'desconhecido'}': {e}", exc_info=True)
                finally: 
                    limpar_janelas_extras_e_focar_principal(driver, main_window_handle)
            
            end_time_numero = time.time()
            tempo_total_numero = end_time_numero - start_time_numero
            print(f"{'-'*32} FIM NÚMERO {numero_display} (Tempo: {tempo_total_numero:.2f}s) {'-'*32}")
            time.sleep(0.6)

        logger.info("Processamento da lista TXT concluído."); print("\n🏁 Processamento da lista TXT finalizado.")

    except FileNotFoundError: 
        print(f"❌ Arquivo TXT não encontrado: {txt_caminho_entrada}"); 
        logger.error(f"Arquivo TXT não encontrado: {txt_caminho_entrada}")
    except InvalidSelectorException as e_selector: 
        print(f"💥 Erro CRÍTICO de seletor XPath: {e_selector}")
        logger.critical(f"Erro CRÍTICO de seletor XPath: {e_selector}", exc_info=True)
    except Exception as e: 
        print(f"💥 Erro crítico: {e}"); 
        logger.critical(f"Erro crítico: {e}", exc_info=True)
    finally:
        if driver: 
            print("🔵 Fechando navegador..."); 
            logger.info("Fechando navegador..."); 
            driver.quit()
        
        if lista_resultados_finais: 
            df = pd.DataFrame(lista_resultados_finais)
            try: 
                df.to_excel(excel_saida_nome, index=False, engine='openpyxl'); 
                print(f"\n💾 Resultados: {excel_saida_nome}"); 
                logger.info(f"Resultados: {excel_saida_nome}")
            except Exception as e:
                print(f"❌ Erro ao salvar Excel: {e}"); 
                logger.error(f"Erro ao salvar Excel: {e}")
                try: 
                    csv_path = excel_saida_nome.replace(".xlsx", ".csv"); 
                    df.to_csv(csv_path, index=False, encoding='utf-8-sig'); 
                    print(f"💾 Resultados (CSV): {csv_path}"); 
                    logger.info(f"Resultados (CSV): {csv_path}")
                except Exception as e2: 
                    print(f"❌ Erro ao salvar CSV: {e2}"); 
                    logger.error(f"Erro ao salvar CSV: {e2}")
        else: 
            print("ℹ️ Nenhum precatório válido foi processado para salvar no Excel."); 
            logger.info("Nenhum precatório válido processado para Excel.")
        
        logger.info("--- FIM DA EXECUÇÃO ---")

if __name__ == "__main__":
    if logger is None: logger = configurar_logs() 
    print("🚀 Iniciando Processador Completo TJSP - Versão TXT...")
    
    # Caminho do arquivo TXT filtrado
    txt_path = os.path.join(SCRIPT_DIR, ARQUIVO_TXT_FILTRADO)
    
    if not os.path.exists(txt_path):
        print(f"❌ Arquivo TXT '{ARQUIVO_TXT_FILTRADO}' não encontrado em:\n  {SCRIPT_DIR}")
        print("Certifique-se de que o arquivo 'autosfiltrados.txt' está na pasta do script.")
        txt_alt = input(f"Ou digite o caminho completo do arquivo TXT (Enter para sair): ").strip()
        if not txt_alt or not os.path.exists(txt_alt): 
            print("Encerrando."); sys.exit(1)
        txt_path = txt_alt
    else: 
        print(f"ℹ️ Usando arquivo TXT: {txt_path}")
    
    # Solicitar número inicial da lista
    num_ini = 1
    try:
        num_in = input(f"🔢 Digite o número da lista para iniciar (padrão: 1): ").strip()
        if num_in.isdigit() and int(num_in) > 0: 
            num_ini = int(num_in)
        elif num_in: 
            print("⚠️ Número inválido, usando padrão 1.")
        else: 
            print("ℹ️ Usando número inicial padrão: 1.")
    except: 
        print("⚠️ Entrada inválida, usando padrão 1.")
    
    # Gerar nome do arquivo de saída
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_out = os.path.join(SCRIPT_DIR, f"resultados_completos_TJSP_TXT_{ts}.xlsx")
    
    # Executar processamento
    processador_completo_tjsp_txt(txt_path, excel_out, num_ini)
    print("\n🏁 Fim do processamento!")
    input("Pressione Enter para sair.")
