2025-06-27 12:33:28 [INFO] - <module> - tjsp_download.py importado.
2025-06-27 12:33:30 [INFO] - configurar_chrome_unificado - Usando diretório de dados do usuário: C:\Users\<USER>\ClineAutomationProfile_TJSP_sami__20250627_123328
2025-06-27 12:33:30 [INFO] - log - ====== WebDriver manager ======
2025-06-27 12:33:31 [INFO] - log - Get LATEST chromedriver version for google-chrome
2025-06-27 12:33:31 [INFO] - log - Get LATEST chromedriver version for google-chrome
2025-06-27 12:33:31 [INFO] - log - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.49\chromedriver-win32/chromedriver.exe] found in cache
2025-06-27 12:33:34 [ERROR] - iniciar_navegador_unificado - Erro ao iniciar o navegador: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0xf84493+62419]
	GetHandleVerifier [0x0xf844d4+62484]
	(No symbol) [0x0xdc2133]
	(No symbol) [0x0xdf5958]
	(No symbol) [0x0xdf1249]
	(No symbol) [0x0xe3af3e]
	(No symbol) [0x0xe3a82a]
	(No symbol) [0x0xe2f266]
	(No symbol) [0x0xdfe852]
	(No symbol) [0x0xdff6f4]
	GetHandleVerifier [0x0x11f4773+2619059]
	GetHandleVerifier [0x0x11efb8a+2599626]
	GetHandleVerifier [0x0xfab03a+221050]
	GetHandleVerifier [0x0xf9b2b8+156152]
	GetHandleVerifier [0x0xfa1c6d+183213]
	GetHandleVerifier [0x0xf8c378+94904]
	GetHandleVerifier [0x0xf8c502+95298]
	GetHandleVerifier [0x0xf7765a+9626]
	BaseThreadInitThunk [0x0x75595d49+25]
	RtlInitializeExceptionChain [0x0x7707d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7707d021+561]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\MontSamm\tjsp_integrado\TJSP_Integrado\TJSP_completo.py", line 145, in iniciar_navegador_unificado
    driver = webdriver.Chrome(service=ChromeService(driver_path), options=chrome_options)
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\chrome\webdriver.py", line 47, in __init__
    super().__init__(
    ~~~~~~~~~~~~~~~~^
        browser_name=DesiredCapabilities.CHROME["browserName"],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        keep_alive=keep_alive,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\chromium\webdriver.py", line 69, in __init__
    super().__init__(command_executor=executor, options=options)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 257, in __init__
    self.start_session(capabilities)
    ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 356, in start_session
    response = self.execute(Command.NEW_SESSION, caps)["value"]
               ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 447, in execute
    self.error_handler.check_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Program Files\Python313\Lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0xf84493+62419]
	GetHandleVerifier [0x0xf844d4+62484]
	(No symbol) [0x0xdc2133]
	(No symbol) [0x0xdf5958]
	(No symbol) [0x0xdf1249]
	(No symbol) [0x0xe3af3e]
	(No symbol) [0x0xe3a82a]
	(No symbol) [0x0xe2f266]
	(No symbol) [0x0xdfe852]
	(No symbol) [0x0xdff6f4]
	GetHandleVerifier [0x0x11f4773+2619059]
	GetHandleVerifier [0x0x11efb8a+2599626]
	GetHandleVerifier [0x0xfab03a+221050]
	GetHandleVerifier [0x0xf9b2b8+156152]
	GetHandleVerifier [0x0xfa1c6d+183213]
	GetHandleVerifier [0x0xf8c378+94904]
	GetHandleVerifier [0x0xf8c502+95298]
	GetHandleVerifier [0x0xf7765a+9626]
	BaseThreadInitThunk [0x0x75595d49+25]
	RtlInitializeExceptionChain [0x0x7707d09b+107]
	RtlGetAppContainerNamedObjectPath [0x0x7707d021+561]

